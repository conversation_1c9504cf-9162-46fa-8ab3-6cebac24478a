import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Layout } from 'antd';
import Sidebar from './components/Sidebar';
import HomePage from './pages/HomePage';
import OrdersPage from './pages/OrdersPage';
import ArrivalsPage from './pages/ArrivalsPage';
import ProductsPage from './pages/ProductsPage';
import MatchingPage from './pages/MatchingPage';
import './App.css';

const { Content } = Layout;

function App() {
  return (
    <Router>
      <Layout style={{ minHeight: '100vh' }}>
        <Sidebar />
        <Layout>
          <Content style={{ padding: '24px', background: '#fff' }}>
            <Routes>
              <Route path="/" element={<HomePage />} />
              <Route path="/orders" element={<OrdersPage />} />
              <Route path="/arrivals" element={<ArrivalsPage />} />
              <Route path="/products" element={<ProductsPage />} />
              <Route path="/inventory" element={<div>庫存管理頁面</div>} />
              <Route path="/matching" element={<MatchingPage />} />
              <Route path="/history" element={<div>商品分配歷史頁面</div>} />
            </Routes>
          </Content>
        </Layout>
      </Layout>
    </Router>
  );
}

export default App;