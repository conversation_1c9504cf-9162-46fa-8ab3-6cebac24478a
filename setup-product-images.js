const fs = require('fs');
const path = require('path');

/**
 * 設置商品圖片 - 將圖片從 src/product_img 複製到 public/images
 * 並創建一個模擬的商品資料檔案供前端使用
 */

// 圖片檔案名稱與商品資料的對應關係
const productImageData = [
  {
    id: 1,
    productId: 'VINYL-DB-ZIGGY-001',
    productName: '<PERSON> - The Rise and Fall of Ziggy Stardust',
    sku: 'VINYL-DB-ZIGGY-001',
    upc: '0190295851418',
    artist: '<PERSON>',
    brand: 'Parlophone',
    supplier: 'AMI',
    category: 'Vinyl Records',
    price: 950.00,
    stockQuantity: 12,
    isPublished: true,
    tags: ['經典', '搖滾', '代購'],
    status: 'active',
    imageFile: '<PERSON> - The Rise and Fall of Ziggy Stardust.webp'
  },
  {
    id: 2,
    productId: 'VINYL-LZ-IV-001',
    productName: 'Led Zeppelin - IV',
    sku: 'VINYL-LZ-IV-001',
    upc: '081227971816',
    artist: 'Led Zeppelin',
    brand: 'Atlantic Records',
    supplier: 'MONO',
    category: 'Vinyl Records',
    price: 750.00,
    stockQuantity: 8,
    isPublished: true,
    tags: ['搖滾', '經典', '韓國'],
    status: 'active',
    imageFile: 'Led Zeppelin - IV.webp'
  },
  {
    id: 3,
    productId: 'VINYL-PF-DSOTM-001',
    productName: 'Pink Floyd - The Dark Side of the Moon',
    sku: 'VINYL-PF-DSOTM-001',
    upc: '0656605310817',
    artist: 'Pink Floyd',
    brand: 'Harvest Records',
    supplier: 'AMI',
    category: 'Vinyl Records',
    price: 850.00,
    stockQuantity: 15,
    isPublished: true,
    tags: ['代購', '經典', '搖滾'],
    status: 'active',
    imageFile: 'Pink Floyd - The Dark Side of the Moon.webp'
  },
  {
    id: 4,
    productId: 'VINYL-QUEEN-BR-001',
    productName: 'Queen - Bohemian Rhapsody',
    sku: 'VINYL-QUEEN-BR-001',
    upc: '0602577208614',
    artist: 'Queen',
    brand: 'Hollywood Records',
    supplier: 'SK1',
    category: 'Vinyl Records',
    price: 680.00,
    stockQuantity: 20,
    isPublished: true,
    tags: ['經典', '搖滾', '電影原聲帶'],
    status: 'active',
    imageFile: 'Queen - Bohemian Rhapsody.webp'
  },
  {
    id: 5,
    productId: 'VINYL-BEATLES-AR-001',
    productName: 'The Beatles - Abbey Road',
    sku: 'VINYL-BEATLES-AR-001',
    upc: '094638241614',
    artist: 'The Beatles',
    brand: 'Apple Records',
    supplier: 'SK1',
    category: 'Vinyl Records',
    price: 1200.00,
    stockQuantity: 5,
    isPublished: true,
    tags: ['粉專排隊', '經典', '限量版'],
    status: 'active',
    imageFile: 'The Beatles - Abbey Road.webp'
  }
];

async function setupProductImages() {
  console.log('=== 設置商品圖片 ===');
  
  try {
    // 檢查 src/product_img 資料夾
    const sourceImageDir = path.join(__dirname, 'src', 'product_img');
    if (!fs.existsSync(sourceImageDir)) {
      throw new Error(`圖片來源資料夾不存在: ${sourceImageDir}`);
    }
    
    // 創建 public/images 資料夾
    const publicDir = path.join(__dirname, 'public');
    const publicImagesDir = path.join(publicDir, 'images');
    
    if (!fs.existsSync(publicDir)) {
      fs.mkdirSync(publicDir);
      console.log('✅ 創建 public 資料夾');
    }
    
    if (!fs.existsSync(publicImagesDir)) {
      fs.mkdirSync(publicImagesDir);
      console.log('✅ 創建 public/images 資料夾');
    }
    
    console.log('\n=== 複製圖片檔案 ===');
    let successCount = 0;
    let errorCount = 0;
    
    // 處理每個商品的圖片
    for (const product of productImageData) {
      try {
        const sourceImagePath = path.join(sourceImageDir, product.imageFile);
        const targetImageName = `product_${product.id}_${product.imageFile}`;
        const targetImagePath = path.join(publicImagesDir, targetImageName);
        
        // 檢查來源檔案是否存在
        if (!fs.existsSync(sourceImagePath)) {
          console.log(`⚠️  來源圖片不存在: ${product.imageFile}`);
          errorCount++;
          continue;
        }
        
        // 複製圖片
        fs.copyFileSync(sourceImagePath, targetImagePath);
        console.log(`✅ 複製圖片: ${product.imageFile} -> ${targetImageName}`);
        
        // 更新商品資料中的圖片 URL
        product.productImages = [`/images/${targetImageName}`];
        delete product.imageFile; // 移除臨時屬性
        
        successCount++;
        
      } catch (error) {
        console.error(`❌ 處理圖片 ${product.imageFile} 時發生錯誤:`, error.message);
        errorCount++;
      }
    }
    
    console.log(`\n✅ 成功複製: ${successCount} 個圖片`);
    console.log(`❌ 複製失敗: ${errorCount} 個圖片`);
    
    // 創建模擬商品資料檔案
    console.log('\n=== 創建模擬商品資料 ===');
    const mockDataPath = path.join(__dirname, 'src', 'data', 'mockProducts.json');
    const mockDataDir = path.dirname(mockDataPath);
    
    if (!fs.existsSync(mockDataDir)) {
      fs.mkdirSync(mockDataDir, { recursive: true });
      console.log('✅ 創建 src/data 資料夾');
    }
    
    // 移除 imageFile 屬性並寫入檔案
    const cleanProductData = productImageData.map(product => {
      const { imageFile, ...cleanProduct } = product;
      return cleanProduct;
    });
    
    fs.writeFileSync(mockDataPath, JSON.stringify(cleanProductData, null, 2), 'utf8');
    console.log(`✅ 創建模擬商品資料檔案: ${mockDataPath}`);
    
    console.log('\n=== 設置完成 ===');
    console.log('現在您可以:');
    console.log('1. 啟動後端服務器: npm start 或 node server.js');
    console.log('2. 啟動前端應用: npm start (在另一個終端)');
    console.log('3. 訪問 http://localhost:3000 查看商品頁面');
    
    // 顯示設置的商品列表
    console.log('\n=== 設置的商品列表 ===');
    cleanProductData.forEach((product, index) => {
      console.log(`${index + 1}. ${product.productName}`);
      console.log(`   - 圖片: ${product.productImages[0]}`);
      console.log(`   - 價格: NT$${product.price}`);
      console.log(`   - 庫存: ${product.stockQuantity}`);
      console.log('');
    });
    
  } catch (error) {
    console.error('❌ 設置失敗:', error.message);
    console.error('詳細錯誤:', error);
  }
}

// 執行設置
setupProductImages().catch(console.error);
