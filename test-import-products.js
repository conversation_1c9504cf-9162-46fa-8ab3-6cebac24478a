// 測試商品匯入功能
const testImportProducts = async () => {
  console.log('=== 測試商品匯入功能 ===');
  
  // 模擬已檢驗完成的商品
  const verifiedProducts = [
    {
      productName: 'The Beatles - Abbey Road (Remastered)',
      upc: '094638241614',
      sku: 'VINYL-BEATLES-AR-RM',
      artist: 'The Beatles',
      brand: 'Apple Records',
      supplier: 'AMI',
      category: 'Vinyl Records',
      price: 950.00,
      expectedQuantity: 5,
      actualQuantity: 5,
      status: 'verified',
      specifications: {
        format: 'LP',
        speed: '33 RPM',
        size: '12 inch'
      },
      tags: ['經典', '重製版', '代購']
    },
    {
      productName: 'Queen - Bohemian Rhapsody (Limited Edition)',
      upc: '050087349721',
      sku: 'VINYL-QUEEN-BR-LE',
      artist: 'Queen',
      brand: 'Hollywood Records',
      supplier: 'MONO',
      category: 'Vinyl Records',
      price: 1200.00,
      expectedQuantity: 3,
      actualQuantity: 3,
      status: 'verified',
      specifications: {
        format: 'LP',
        speed: '33 RPM',
        size: '12 inch',
        edition: 'Limited'
      },
      tags: ['限量版', '經典', '搖滾']
    }
  ];
  
  // 模擬未檢驗完成的商品
  const unverifiedProducts = [
    {
      productName: 'David Bowie - The Rise and Fall of Ziggy Stardust',
      upc: '190295851927',
      sku: 'VINYL-BOWIE-ZS',
      artist: 'David Bowie',
      brand: 'Parlophone',
      supplier: 'SK1',
      category: 'Vinyl Records',
      price: 850.00,
      expectedQuantity: 4,
      actualQuantity: 2, // 數量不符
      status: 'discrepancy',
      specifications: {
        format: 'LP',
        speed: '33 RPM',
        size: '12 inch'
      },
      tags: ['經典', '搖滾', '藝術搖滾']
    }
  ];
  
  try {
    console.log('\n1. 測試匯入已檢驗完成的商品...');
    const verifiedResponse = await fetch('http://localhost:5000/api/products/import', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        products: verifiedProducts, 
        type: 'verified' 
      })
    });
    
    const verifiedResult = await verifiedResponse.json();
    console.log('已檢驗商品匯入結果:', verifiedResult);
    
    console.log('\n2. 測試匯入所有商品（包含未檢驗）...');
    const allResponse = await fetch('http://localhost:5000/api/products/import', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        products: [...verifiedProducts, ...unverifiedProducts], 
        type: 'all' 
      })
    });
    
    const allResult = await allResponse.json();
    console.log('所有商品匯入結果:', allResult);
    
    console.log('\n3. 檢查商品資料庫...');
    const productsResponse = await fetch('http://localhost:5000/api/products');
    const products = await productsResponse.json();
    console.log(`商品資料庫中共有 ${products.length} 個商品:`);
    products.forEach((product, index) => {
      console.log(`${index + 1}. ${product.productName} (${product.isPublished ? '已上架' : '未上架'})`);
    });
    
    console.log('\n✅ 商品匯入功能測試完成');
    
  } catch (error) {
    console.error('❌ 測試失敗:', error);
  }
};

// 執行測試
testImportProducts();
