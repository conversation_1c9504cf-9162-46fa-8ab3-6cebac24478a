const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const sequelize = require('./src/config/database');
const Order = require('./src/models/Order');
const ProductInfo = require('./src/models/ProductInfo');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

// 中間件
app.use(cors());
app.use(express.json());

// 提供靜態檔案服務 (圖片)
app.use('/images', express.static(path.join(__dirname, 'public', 'images')));

// 數據庫連接
sequelize.authenticate()
  .then(() => {
    console.log('✅ 數據庫連接成功');
    // 同步數據庫模型
    return sequelize.sync();
  })
  .then(() => {
    console.log('✅ 數據庫模型同步成功');
  })
  .catch(err => {
    console.error('❌ 數據庫連接錯誤:', err);
  });

// API 路由

// ===== 商品相關 API =====

// 獲取所有商品
app.get('/api/products', async (req, res) => {
  try {
    console.log('收到獲取商品請求');

    // 嘗試從資料庫獲取商品
    try {
      await sequelize.authenticate();
      const products = await ProductInfo.findAll({
        order: [['createdAt', 'DESC']]
      });

      // 轉換為前端需要的格式
      const formattedProducts = products.map(product => ({
        id: product.id,
        productId: product.productId,
        productName: product.productName,
        sku: product.sku,
        upc: product.upc,
        artist: product.artist,
        brand: product.brand,
        supplier: product.supplier,
        category: product.category,
        price: parseFloat(product.price) || 0,
        stockQuantity: product.stockQuantity || 0,
        isPublished: product.isPublished,
        productImages: product.productImages || [],
        tags: product.tags || [],
        status: product.status || 'active',
        specifications: product.specifications || {},
        createdAt: product.createdAt,
        updatedAt: product.updatedAt
      }));

      console.log(`從資料庫找到 ${formattedProducts.length} 個商品`);
      res.json(formattedProducts);

    } catch (dbError) {
      console.log('資料庫連接失敗，使用模擬資料:', dbError.message);

      // 嘗試讀取模擬資料
      try {
        const mockDataPath = path.join(__dirname, 'src', 'data', 'mockProducts.json');
        if (fs.existsSync(mockDataPath)) {
          const mockData = JSON.parse(fs.readFileSync(mockDataPath, 'utf8'));
          console.log(`使用模擬資料，找到 ${mockData.length} 個商品`);
          res.json(mockData);
        } else {
          throw new Error('模擬資料檔案不存在');
        }
      } catch (mockError) {
        console.error('讀取模擬資料失敗:', mockError.message);
        // 返回空陣列作為最後的備案
        res.json([]);
      }
    }

  } catch (error) {
    console.error('獲取商品數據失敗:', error);
    res.status(500).json({
      error: '無法獲取商品數據',
      message: error.message
    });
  }
});

// 獲取單個商品詳情
app.get('/api/products/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`收到獲取商品詳情請求: ${id}`);

    const product = await ProductInfo.findByPk(id);

    if (!product) {
      return res.status(404).json({ error: '商品不存在' });
    }

    res.json(product);

  } catch (error) {
    console.error('獲取商品詳情失敗:', error);
    res.status(500).json({
      error: '無法獲取商品詳情',
      message: error.message
    });
  }
});

// ===== 訂單相關 API =====

// 獲取所有訂單
app.get('/api/orders', async (req, res) => {
  try {
    console.log('收到獲取訂單請求');
    
    const orders = await Order.findAll({
      order: [['createdAt', 'DESC']]
    });
    
    console.log(`找到 ${orders.length} 筆訂單`);
    res.json(orders);
    
  } catch (error) {
    console.error('獲取訂單失敗:', error);
    res.status(500).json({ 
      error: '無法獲取訂單數據',
      message: error.message 
    });
  }
});

// 獲取單個訂單詳情
app.get('/api/orders/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`收到獲取訂單詳情請求: ${id}`);
    
    const order = await Order.findByPk(id);
    
    if (!order) {
      return res.status(404).json({ error: '訂單不存在' });
    }
    
    res.json(order);
    
  } catch (error) {
    console.error('獲取訂單詳情失敗:', error);
    res.status(500).json({ 
      error: '無法獲取訂單詳情',
      message: error.message 
    });
  }
});

// 更新訂單狀態
app.put('/api/orders/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    
    console.log(`更新訂單 ${id} 狀態為: ${status}`);
    
    const order = await Order.findByPk(id);
    
    if (!order) {
      return res.status(404).json({ error: '訂單不存在' });
    }
    
    await order.update({ status });
    
    res.json({ 
      success: true, 
      message: '訂單狀態更新成功',
      order 
    });
    
  } catch (error) {
    console.error('更新訂單狀態失敗:', error);
    res.status(500).json({ 
      error: '無法更新訂單狀態',
      message: error.message 
    });
  }
});

// 同步 EasyStore 訂單（暫時返回成功消息）
app.post('/api/orders/sync', async (req, res) => {
  try {
    console.log('收到同步訂單請求');
    
    // 這裡暫時返回成功消息，之後可以實現真正的 EasyStore 同步
    res.json({ 
      success: true, 
      message: '訂單同步功能開發中，目前顯示數據庫中的現有訂單' 
    });
    
  } catch (error) {
    console.error('同步訂單失敗:', error);
    res.status(500).json({ 
      error: '同步訂單失敗',
      message: error.message 
    });
  }
});

// 獲取訂單統計信息
app.get('/api/orders/stats', async (req, res) => {
  try {
    console.log('收到獲取訂單統計請求');
    
    const totalOrders = await Order.count();
    const pendingOrders = await Order.count({ where: { status: 'pending' } });
    const completedOrders = await Order.count({ where: { status: 'completed' } });
    const processingOrders = await Order.count({ where: { status: 'processing' } });
    
    const stats = {
      total: totalOrders,
      pending: pendingOrders,
      completed: completedOrders,
      processing: processingOrders
    };
    
    console.log('訂單統計:', stats);
    res.json(stats);
    
  } catch (error) {
    console.error('獲取訂單統計失敗:', error);
    res.status(500).json({ 
      error: '無法獲取訂單統計',
      message: error.message 
    });
  }
});

// 健康檢查端點
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    message: 'API 服務器運行正常',
    timestamp: new Date().toISOString()
  });
});

// 錯誤處理中間件
app.use((err, req, res, next) => {
  console.error('服務器錯誤:', err);
  res.status(500).json({ 
    error: '服務器內部錯誤',
    message: err.message 
  });
});

// 404 處理
app.use((req, res) => {
  res.status(404).json({
    error: '端點不存在',
    path: req.originalUrl
  });
});

// 啟動服務器
app.listen(PORT, () => {
  console.log(`🚀 API 服務器運行在 http://localhost:${PORT}`);
  console.log(`📊 健康檢查: http://localhost:${PORT}/api/health`);
  console.log(`📋 訂單 API: http://localhost:${PORT}/api/orders`);
});
