// 測試 EasyStore API 同步功能
const testEasyStoreSync = async () => {
  console.log('=== 測試 EasyStore API 同步功能 ===');
  
  try {
    console.log('\n1. 檢查同步前的數據庫狀態...');
    const beforeResponse = await fetch('http://localhost:5000/api/products');
    const beforeProducts = await beforeResponse.json();
    console.log(`同步前數據庫中有 ${beforeProducts.length} 個商品`);
    
    console.log('\n2. 開始從 EasyStore 同步商品...');
    const syncResponse = await fetch('http://localhost:5000/api/products/sync-easystore', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    const syncResult = await syncResponse.json();
    console.log('\n同步結果:', JSON.stringify(syncResult, null, 2));
    
    if (syncResult.success) {
      console.log(`\n✅ 同步成功！`);
      console.log(`- 新增商品: ${syncResult.syncedCount}`);
      console.log(`- 更新商品: ${syncResult.updatedCount}`);
      console.log(`- 總處理數: ${syncResult.totalProcessed}`);
      console.log(`- EasyStore 總商品數: ${syncResult.easyStoreTotal}`);
    } else {
      console.log(`\n❌ 同步失敗: ${syncResult.message}`);
      return;
    }
    
    console.log('\n3. 檢查同步後的數據庫狀態...');
    const afterResponse = await fetch('http://localhost:5000/api/products');
    const afterProducts = await afterResponse.json();
    console.log(`同步後數據庫中有 ${afterProducts.length} 個商品`);
    
    console.log('\n4. 顯示同步的商品詳情...');
    const easyStoreProducts = afterProducts.filter(product => 
      product.supplier === 'EasyStore'
    );
    
    console.log(`\nEasyStore 商品 (${easyStoreProducts.length} 個):`);
    easyStoreProducts.forEach((product, index) => {
      console.log(`${index + 1}. ${product.productName}`);
      console.log(`   - SKU: ${product.sku}`);
      console.log(`   - 價格: NT$${product.price}`);
      console.log(`   - 庫存: ${product.stockQuantity}`);
      console.log(`   - 狀態: ${product.isPublished ? '已上架' : '未上架'}`);
      console.log(`   - 品牌: ${product.brand}`);
      console.log(`   - 分類: ${product.category || '無'}`);
      console.log('');
    });
    
    console.log('\n5. 驗證商品資料完整性...');
    let validationErrors = 0;
    
    easyStoreProducts.forEach(product => {
      if (!product.productName) {
        console.log(`❌ 商品名稱缺失: ${product.sku}`);
        validationErrors++;
      }
      if (!product.sku) {
        console.log(`❌ SKU 缺失: ${product.productName}`);
        validationErrors++;
      }
      if (product.price < 0) {
        console.log(`❌ 價格異常: ${product.productName} - ${product.price}`);
        validationErrors++;
      }
    });
    
    if (validationErrors === 0) {
      console.log('✅ 所有商品資料驗證通過');
    } else {
      console.log(`⚠️ 發現 ${validationErrors} 個資料問題`);
    }
    
    console.log('\n✅ EasyStore API 同步測試完成');
    
  } catch (error) {
    console.error('\n❌ 測試失敗:', error.message);
    if (error.message.includes('fetch')) {
      console.log('提示：請確保後端服務器正在運行 (node test-server.js)');
    }
  }
};

// 執行測試
testEasyStoreSync();
