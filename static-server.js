const express = require('express');
const path = require('path');
const cors = require('cors');

const app = express();
const PORT = 5000;

// 啟用 CORS
app.use(cors());

// 提供靜態檔案服務
app.use('/images', express.static(path.join(__dirname, 'public', 'images')));

// 健康檢查
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: '靜態服務器運行正常' });
});

// 列出所有圖片
app.get('/images-list', (req, res) => {
  const fs = require('fs');
  const imagesDir = path.join(__dirname, 'public', 'images');
  
  try {
    const files = fs.readdirSync(imagesDir);
    const imageFiles = files.filter(file => 
      file.toLowerCase().endsWith('.webp') || 
      file.toLowerCase().endsWith('.jpg') || 
      file.toLowerCase().endsWith('.jpeg') || 
      file.toLowerCase().endsWith('.png')
    );
    
    res.json({
      count: imageFiles.length,
      images: imageFiles.map(file => `/images/${file}`)
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.listen(PORT, () => {
  console.log(`靜態服務器啟動在 http://localhost:${PORT}`);
  console.log('圖片服務: http://localhost:5000/images/');
  console.log('圖片列表: http://localhost:5000/images-list');
});
