const sequelize = require('./src/config/database');
const ProductInfo = require('./src/models/ProductInfo');
const { syncSingleProduct } = require('./src/services/easyStoreProductService');
require('dotenv').config();

/**
 * 測試商品同步功能
 */
async function testProductSync() {
  console.log('=== 測試 EasyStore 商品同步 ===');
  
  try {
    // 測試數據庫連接
    console.log('正在測試數據庫連接...');
    await sequelize.authenticate();
    console.log('✅ 數據庫連接成功');
    
    // 同步數據庫模型
    console.log('正在同步數據庫模型...');
    await sequelize.sync();
    console.log('✅ 數據庫模型同步成功');
    
    // 測試商品ID
    const testProductId = '0656605310817';
    console.log(`\n開始同步測試商品: ${testProductId}`);
    
    // 同步商品
    const syncedProduct = await syncSingleProduct(testProductId);
    
    console.log('\n=== 同步結果 ===');
    console.log('商品ID:', syncedProduct.productId);
    console.log('商品名稱:', syncedProduct.productName);
    console.log('SKU:', syncedProduct.sku);
    console.log('UPC:', syncedProduct.upc);
    console.log('藝術家:', syncedProduct.artist);
    console.log('品牌:', syncedProduct.brand);
    console.log('供應商:', syncedProduct.supplier);
    console.log('分類:', syncedProduct.category);
    console.log('價格:', syncedProduct.price);
    console.log('庫存數量:', syncedProduct.stockQuantity);
    console.log('是否已上架:', syncedProduct.isPublished);
    console.log('商品圖片數量:', syncedProduct.productImages ? syncedProduct.productImages.length : 0);
    console.log('商品變體數量:', syncedProduct.productVariants ? syncedProduct.productVariants.length : 0);
    console.log('標籤:', syncedProduct.tags);
    console.log('最後同步時間:', syncedProduct.lastSyncAt);
    
    // 查詢數據庫中的所有商品
    console.log('\n=== 數據庫中的所有商品 ===');
    const allProducts = await ProductInfo.findAll({
      order: [['createdAt', 'DESC']]
    });
    
    console.log(`找到 ${allProducts.length} 個商品:`);
    allProducts.forEach((product, index) => {
      console.log(`${index + 1}. ${product.productName} (ID: ${product.productId}) - 庫存: ${product.stockQuantity}`);
    });
    
    console.log('\n✅ 商品同步測試完成');
    
  } catch (error) {
    console.error('\n❌ 商品同步測試失敗:', error.message);
    
    if (error.response) {
      console.error('API 響應狀態:', error.response.status);
      console.error('API 響應數據:', JSON.stringify(error.response.data, null, 2));
    }
    
    console.error('詳細錯誤:', error);
  } finally {
    // 關閉數據庫連接
    await sequelize.close();
    console.log('\n數據庫連接已關閉');
  }
}

// 執行測試
testProductSync().catch(console.error);
