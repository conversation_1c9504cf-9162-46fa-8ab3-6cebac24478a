.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 到貨商品頁面自定義樣式 */
.row-completed {
  background-color: #f6ffed !important;
}

.row-discrepancy {
  background-color: #fff2f0 !important;
}

.row-checking {
  background-color: #e6f7ff !important;
}

.row-pending {
  background-color: #fafafa !important;
}

/* 掃碼輸入框樣式 */
.ant-input-affix-wrapper-focused {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 商品圖片容器樣式 */
.product-image-container {
  transition: all 0.3s ease;
  border-radius: 8px;
  overflow: hidden;
}

.product-image-container:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 統計卡片樣式 */
.ant-statistic-content {
  font-size: 20px;
}

.ant-statistic-title {
  font-size: 14px;
  color: #666;
}

/* 表格行動畫 */
.ant-table-tbody > tr {
  transition: background-color 0.3s ease;
}

.ant-table-tbody > tr:hover {
  background-color: #fafafa !important;
}

/* 數量調整控件樣式 */
.quantity-control {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.quantity-control .ant-btn {
  border: 1px solid #d9d9d9;
  transition: all 0.2s ease;
}

.quantity-control .ant-btn:hover {
  border-color: #40a9ff;
  color: #40a9ff;
}

.quantity-control .ant-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.quantity-control .ant-input-number {
  border-radius: 4px;
  text-align: center;
}

.quantity-control .ant-input-number-input {
  text-align: center;
  font-weight: bold;
}

/* 數量顯示顏色 */
.quantity-completed {
  color: #52c41a !important;
}

.quantity-discrepancy {
  color: #ff4d4f !important;
}

.quantity-checking {
  color: #faad14 !important;
}

/* 掃碼輸入框特殊樣式 */
.scan-input-focused {
  border-color: #52c41a !important;
  box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2) !important;
}

/* 商品圖片邊框動畫 */
.product-image-success {
  border: 3px solid #52c41a !important;
  animation: pulse-success 1s ease-in-out;
}

.product-image-error {
  border: 3px solid #ff4d4f !important;
  animation: pulse-error 1s ease-in-out;
}

@keyframes pulse-success {
  0% { box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(82, 196, 26, 0); }
  100% { box-shadow: 0 0 0 0 rgba(82, 196, 26, 0); }
}

@keyframes pulse-error {
  0% { box-shadow: 0 0 0 0 rgba(255, 77, 79, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(255, 77, 79, 0); }
  100% { box-shadow: 0 0 0 0 rgba(255, 77, 79, 0); }
}

/* 表格橫向滾動條樣式 */
.ant-table-body {
  overflow-x: auto !important;
}

.ant-table-body::-webkit-scrollbar {
  height: 8px;
}

.ant-table-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.ant-table-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.ant-table-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 表格固定列寬樣式 */
.ant-table-thead > tr > th {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.ant-table-tbody > tr > td {
  white-space: nowrap;
  padding: 8px 12px;
}

/* 商品名稱列特殊樣式 */
.ant-table-tbody > tr > td:first-child {
  white-space: normal; /* 允許商品名稱換行 */
}

/* 表格容器樣式 */
.ant-table-container {
  border-radius: 6px;
  overflow: hidden;
}

/* 表格頭部樣式 */
.ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
  border-bottom: 2px solid #f0f0f0;
}

/* 表格邊框樣式 */
.ant-table-bordered .ant-table-container {
  border: 1px solid #f0f0f0;
}

/* 響應式表格樣式 */
@media (max-width: 1200px) {
  .ant-table-body {
    overflow-x: scroll !important;
  }
}
