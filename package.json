{"name": "product-matching-system", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^6.0.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "antd": "^5.26.5", "axios": "^1.10.0", "concurrently": "^9.2.0", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "node-fetch": "^2.7.0", "pg": "^8.16.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.7.0", "react-scripts": "5.0.1", "sequelize": "^6.37.7", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "server": "node server.js", "dev": "concurrently \"npm run server\" \"npm run start\""}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}