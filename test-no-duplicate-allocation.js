// 測試防重複分派功能
const testNoDuplicateAllocation = async () => {
  console.log('=== 測試防重複分派功能 ===');
  
  try {
    console.log('\n1. 檢查初始庫存狀態...');
    const initialResponse = await fetch('http://localhost:5000/api/products');
    const initialProducts = await initialResponse.json();
    
    const pinkFloyd = initialProducts.find(p => p.sku === 'VINYL-PF-DSOTM-001');
    if (pinkFloyd) {
      console.log(`Pink Floyd 初始庫存: ${pinkFloyd.stockQuantity}`);
    }
    
    console.log('\n2. 第一次分派測試...');
    
    // 模擬第一次分派
    const firstAllocation = [
      {
        productId: pinkFloyd.id,
        quantity: 1,
        orderId: 1,
        orderItemId: 1
      }
    ];
    
    const firstResponse = await fetch('http://localhost:5000/api/products/allocate', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ allocations: firstAllocation })
    });
    
    const firstResult = await firstResponse.json();
    console.log('第一次分派結果:', firstResult);
    
    // 檢查庫存變化
    const afterFirstResponse = await fetch('http://localhost:5000/api/products');
    const afterFirstProducts = await afterFirstResponse.json();
    const pinkFloydAfterFirst = afterFirstProducts.find(p => p.sku === 'VINYL-PF-DSOTM-001');
    
    console.log(`第一次分派後 Pink Floyd 庫存: ${pinkFloydAfterFirst.stockQuantity}`);
    console.log(`庫存變化: ${pinkFloyd.stockQuantity} -> ${pinkFloydAfterFirst.stockQuantity}`);
    
    console.log('\n3. 第二次分派測試（應該不會重複分派）...');
    
    // 模擬第二次分派（相同的訂單項目）
    const secondAllocation = [
      {
        productId: pinkFloyd.id,
        quantity: 1,
        orderId: 1,
        orderItemId: 1
      }
    ];
    
    const secondResponse = await fetch('http://localhost:5000/api/products/allocate', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ allocations: secondAllocation })
    });
    
    const secondResult = await secondResponse.json();
    console.log('第二次分派結果:', secondResult);
    
    // 檢查庫存是否保持不變
    const afterSecondResponse = await fetch('http://localhost:5000/api/products');
    const afterSecondProducts = await afterSecondResponse.json();
    const pinkFloydAfterSecond = afterSecondProducts.find(p => p.sku === 'VINYL-PF-DSOTM-001');
    
    console.log(`第二次分派後 Pink Floyd 庫存: ${pinkFloydAfterSecond.stockQuantity}`);
    console.log(`庫存變化: ${pinkFloydAfterFirst.stockQuantity} -> ${pinkFloydAfterSecond.stockQuantity}`);
    
    console.log('\n4. 驗證結果...');
    
    const expectedAfterFirst = pinkFloyd.stockQuantity - 1;
    const actualAfterFirst = pinkFloydAfterFirst.stockQuantity;
    const actualAfterSecond = pinkFloydAfterSecond.stockQuantity;
    
    console.log(`預期第一次分派後庫存: ${expectedAfterFirst}`);
    console.log(`實際第一次分派後庫存: ${actualAfterFirst}`);
    console.log(`實際第二次分派後庫存: ${actualAfterSecond}`);
    
    if (actualAfterFirst === expectedAfterFirst) {
      console.log('✅ 第一次分派正確：庫存正確扣減');
    } else {
      console.log('❌ 第一次分派錯誤：庫存扣減不正確');
    }
    
    if (actualAfterSecond === actualAfterFirst) {
      console.log('✅ 防重複分派正確：第二次分派沒有重複扣減庫存');
    } else {
      console.log('❌ 防重複分派失敗：第二次分派重複扣減了庫存');
    }
    
    console.log('\n5. 測試不同訂單項目的分派...');
    
    // 測試不同的訂單項目（應該可以分派）
    const differentAllocation = [
      {
        productId: pinkFloyd.id,
        quantity: 1,
        orderId: 2, // 不同的訂單ID
        orderItemId: 2
      }
    ];
    
    const differentResponse = await fetch('http://localhost:5000/api/products/allocate', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ allocations: differentAllocation })
    });
    
    const differentResult = await differentResponse.json();
    console.log('不同訂單項目分派結果:', differentResult);
    
    // 檢查庫存變化
    const afterDifferentResponse = await fetch('http://localhost:5000/api/products');
    const afterDifferentProducts = await afterDifferentResponse.json();
    const pinkFloydAfterDifferent = afterDifferentProducts.find(p => p.sku === 'VINYL-PF-DSOTM-001');
    
    console.log(`不同訂單分派後 Pink Floyd 庫存: ${pinkFloydAfterDifferent.stockQuantity}`);
    
    const expectedAfterDifferent = actualAfterSecond - 1;
    if (pinkFloydAfterDifferent.stockQuantity === expectedAfterDifferent) {
      console.log('✅ 不同訂單項目分派正確：庫存正確扣減');
    } else {
      console.log('❌ 不同訂單項目分派錯誤：庫存扣減不正確');
    }
    
    console.log('\n=== 測試總結 ===');
    console.log(`初始庫存: ${pinkFloyd.stockQuantity}`);
    console.log(`第一次分派後: ${actualAfterFirst} (扣減 ${pinkFloyd.stockQuantity - actualAfterFirst})`);
    console.log(`第二次分派後: ${actualAfterSecond} (扣減 ${actualAfterFirst - actualAfterSecond})`);
    console.log(`不同訂單分派後: ${pinkFloydAfterDifferent.stockQuantity} (扣減 ${actualAfterSecond - pinkFloydAfterDifferent.stockQuantity})`);
    
    console.log('\n✅ 防重複分派功能測試完成');
    
  } catch (error) {
    console.error('❌ 測試失敗:', error);
  }
};

// 執行測試
testNoDuplicateAllocation();
