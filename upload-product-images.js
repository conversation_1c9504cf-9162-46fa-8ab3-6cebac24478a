const fs = require('fs');
const path = require('path');
const sequelize = require('./src/config/database');
const ProductInfo = require('./src/models/ProductInfo');
require('dotenv').config();

/**
 * 上傳商品圖片到資料庫
 * 將 product_img 資料夾中的圖片與資料庫中的商品進行匹配並更新
 */

// 圖片檔案名稱與商品名稱的對應關係
const imageProductMapping = {
  '<PERSON> - The Rise and Fall of Ziggy Stardust.webp': '<PERSON> - The Rise and Fall of Ziggy Stardust',
  'Led Zeppelin - IV.webp': 'Led Zeppelin - IV',
  'Pink Floyd - The Dark Side of the Moon.webp': 'Pink Floyd - The Dark Side of the Moon',
  'Queen - Bohemian Rhapsody.webp': 'Queen - Bohemian Rhapsody',
  'The Beatles - Abbey Road.webp': 'The Beatles - Abbey Road'
};

async function uploadProductImages() {
  console.log('=== 開始上傳商品圖片到資料庫 ===');
  
  try {
    // 測試資料庫連接
    await sequelize.authenticate();
    console.log('✅ 資料庫連接成功');
    
    // 同步資料庫模型
    await sequelize.sync();
    console.log('✅ 資料庫模型同步成功');
    
    // 檢查 product_img 資料夾
    const imageDir = path.join(__dirname, 'src', 'product_img');
    if (!fs.existsSync(imageDir)) {
      throw new Error(`圖片資料夾不存在: ${imageDir}`);
    }
    
    // 讀取圖片檔案
    const imageFiles = fs.readdirSync(imageDir).filter(file => 
      file.toLowerCase().endsWith('.webp') || 
      file.toLowerCase().endsWith('.jpg') || 
      file.toLowerCase().endsWith('.jpeg') || 
      file.toLowerCase().endsWith('.png')
    );
    
    console.log(`\n找到 ${imageFiles.length} 個圖片檔案:`);
    imageFiles.forEach(file => console.log(`  - ${file}`));
    
    // 創建 public/images 資料夾來存放圖片
    const publicImagesDir = path.join(__dirname, 'public', 'images');
    if (!fs.existsSync(path.join(__dirname, 'public'))) {
      fs.mkdirSync(path.join(__dirname, 'public'));
    }
    if (!fs.existsSync(publicImagesDir)) {
      fs.mkdirSync(publicImagesDir);
    }
    
    console.log('\n=== 開始處理圖片 ===');
    
    let successCount = 0;
    let errorCount = 0;
    
    for (const imageFile of imageFiles) {
      try {
        console.log(`\n處理圖片: ${imageFile}`);
        
        // 根據檔案名稱找到對應的商品名稱
        const productNamePattern = imageProductMapping[imageFile];
        if (!productNamePattern) {
          console.log(`⚠️  找不到對應的商品名稱模式: ${imageFile}`);
          errorCount++;
          continue;
        }
        
        // 在資料庫中搜尋匹配的商品
        const products = await ProductInfo.findAll({
          where: sequelize.where(
            sequelize.fn('LOWER', sequelize.col('productName')),
            'LIKE',
            `%${productNamePattern.toLowerCase()}%`
          )
        });
        
        if (products.length === 0) {
          console.log(`⚠️  在資料庫中找不到匹配的商品: ${productNamePattern}`);
          errorCount++;
          continue;
        }
        
        if (products.length > 1) {
          console.log(`⚠️  找到多個匹配的商品 (${products.length} 個):`);
          products.forEach(p => console.log(`     - ${p.productName}`));
          console.log(`   使用第一個匹配的商品: ${products[0].productName}`);
        }
        
        const product = products[0];
        console.log(`✅ 找到匹配商品: ${product.productName}`);
        
        // 複製圖片到 public/images 資料夾
        const sourceImagePath = path.join(imageDir, imageFile);
        const targetImageName = `product_${product.id}_${imageFile}`;
        const targetImagePath = path.join(publicImagesDir, targetImageName);
        
        fs.copyFileSync(sourceImagePath, targetImagePath);
        console.log(`✅ 圖片已複製到: ${targetImagePath}`);
        
        // 建立圖片 URL
        const imageUrl = `/images/${targetImageName}`;
        
        // 更新商品的 productImages 欄位
        const currentImages = product.productImages || [];
        const updatedImages = [...currentImages];
        
        // 如果圖片 URL 不存在，則添加
        if (!updatedImages.includes(imageUrl)) {
          updatedImages.unshift(imageUrl); // 添加到陣列開頭作為主圖片
        }
        
        await product.update({
          productImages: updatedImages
        });
        
        console.log(`✅ 商品圖片已更新: ${product.productName}`);
        console.log(`   圖片 URL: ${imageUrl}`);
        successCount++;
        
      } catch (error) {
        console.error(`❌ 處理圖片 ${imageFile} 時發生錯誤:`, error.message);
        errorCount++;
      }
    }
    
    console.log('\n=== 上傳完成 ===');
    console.log(`✅ 成功處理: ${successCount} 個圖片`);
    console.log(`❌ 處理失敗: ${errorCount} 個圖片`);
    
    // 顯示更新後的商品資訊
    console.log('\n=== 檢查更新後的商品 ===');
    const updatedProducts = await ProductInfo.findAll({
      where: {
        productImages: {
          [sequelize.Op.ne]: null
        }
      },
      order: [['updatedAt', 'DESC']]
    });
    
    console.log(`找到 ${updatedProducts.length} 個有圖片的商品:`);
    updatedProducts.forEach(product => {
      console.log(`  - ${product.productName}`);
      console.log(`    圖片: ${JSON.stringify(product.productImages)}`);
    });
    
  } catch (error) {
    console.error('❌ 上傳圖片失敗:', error.message);
    console.error('詳細錯誤:', error);
  } finally {
    await sequelize.close();
    console.log('\n資料庫連接已關閉');
  }
}

// 執行上傳
uploadProductImages().catch(console.error);
