const axios = require('axios');
const ProductInfo = require('../models/ProductInfo');
require('dotenv').config();

/**
 * 從 EasyStore API 獲取商品數據
 */
const fetchProductFromEasyStore = async (productId) => {
  try {
    console.log(`開始從 EasyStore 獲取商品: ${productId}`);
    
    const apiUrl = `https://api.easystore.co/api/3.0/products/${productId}.json`;
    console.log('API URL:', apiUrl);
    
    const response = await axios.get(apiUrl, {
      headers: {
        'Authorization': `Bearer ${process.env.EASYSTORE_API_KEY}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });
    
    if (!response.data) {
      throw new Error('EasyStore API 返回的數據為空');
    }
    
    console.log('✅ 成功獲取商品數據');
    return response.data;
    
  } catch (error) {
    console.error(`❌ 獲取商品 ${productId} 失敗:`, error.message);
    
    if (error.response) {
      console.error('響應狀態碼:', error.response.status);
      console.error('響應數據:', error.response.data);
    }
    
    throw error;
  }
};

/**
 * 將 EasyStore 商品數據轉換為我們的數據格式
 */
const transformProductData = (easyStoreProduct) => {
  try {
    // 處理商品圖片
    const productImages = [];
    if (easyStoreProduct.images && Array.isArray(easyStoreProduct.images)) {
      easyStoreProduct.images.forEach(image => {
        if (image.url) {
          productImages.push(image.url);
        }
      });
    }
    
    // 處理商品變體
    const productVariants = [];
    if (easyStoreProduct.variants && Array.isArray(easyStoreProduct.variants)) {
      easyStoreProduct.variants.forEach(variant => {
        productVariants.push({
          id: variant.id,
          title: variant.title,
          price: variant.price,
          sku: variant.sku,
          inventory_quantity: variant.inventory_quantity,
          weight: variant.weight
        });
      });
    }
    
    // 處理標籤
    const tags = [];
    if (easyStoreProduct.tags) {
      if (typeof easyStoreProduct.tags === 'string') {
        tags.push(...easyStoreProduct.tags.split(',').map(tag => tag.trim()));
      } else if (Array.isArray(easyStoreProduct.tags)) {
        tags.push(...easyStoreProduct.tags);
      }
    }
    
    // 計算庫存數量
    let totalStock = 0;
    if (productVariants.length > 0) {
      totalStock = productVariants.reduce((sum, variant) => {
        return sum + (variant.inventory_quantity || 0);
      }, 0);
    } else if (easyStoreProduct.inventory_quantity) {
      totalStock = easyStoreProduct.inventory_quantity;
    }
    
    // 轉換後的商品數據
    const transformedProduct = {
      productId: easyStoreProduct.id.toString(),
      productName: easyStoreProduct.title || '未知商品',
      sku: easyStoreProduct.sku || null,
      upc: easyStoreProduct.barcode || null,
      artist: extractArtistFromTitle(easyStoreProduct.title),
      brand: easyStoreProduct.vendor || null,
      supplier: determineSupplier(easyStoreProduct),
      category: easyStoreProduct.product_type || null,
      price: parseFloat(easyStoreProduct.price) || 0,
      costPrice: parseFloat(easyStoreProduct.cost_price) || null,
      stockQuantity: totalStock,
      reservedQuantity: 0, // 需要從其他API獲取
      availableQuantity: totalStock,
      isPublished: easyStoreProduct.published || false,
      publishedAt: easyStoreProduct.published_at ? new Date(easyStoreProduct.published_at) : null,
      productImages: productImages,
      productVariants: productVariants,
      tags: tags,
      description: easyStoreProduct.body_html || null,
      specifications: extractSpecifications(easyStoreProduct),
      weight: parseFloat(easyStoreProduct.weight) || null,
      dimensions: extractDimensions(easyStoreProduct),
      status: easyStoreProduct.status === 'active' ? 'active' : 'inactive',
      easyStoreData: easyStoreProduct,
      lastSyncAt: new Date()
    };
    
    return transformedProduct;
    
  } catch (error) {
    console.error('轉換商品數據失敗:', error);
    throw error;
  }
};

/**
 * 從商品標題中提取藝術家信息
 */
const extractArtistFromTitle = (title) => {
  if (!title) return null;
  
  // 常見的分隔符模式
  const patterns = [
    /^([^-]+)\s*-\s*(.+)$/,  // "Artist - Album"
    /^([^–]+)\s*–\s*(.+)$/,  // "Artist – Album" (em dash)
    /^([^:]+)\s*:\s*(.+)$/   // "Artist: Album"
  ];
  
  for (const pattern of patterns) {
    const match = title.match(pattern);
    if (match) {
      return match[1].trim();
    }
  }
  
  return null;
};

/**
 * 根據商品信息確定供應商
 */
const determineSupplier = (product) => {
  const vendor = product.vendor?.toLowerCase() || '';
  const tags = product.tags?.toLowerCase() || '';
  
  if (vendor.includes('ami') || tags.includes('ami')) return 'AMI';
  if (vendor.includes('mono') || tags.includes('mono')) return 'MONO';
  if (vendor.includes('sk1') || tags.includes('sk1')) return 'SK1';
  
  return product.vendor || null;
};

/**
 * 提取商品規格信息
 */
const extractSpecifications = (product) => {
  const specs = {};
  
  if (product.product_type) specs.type = product.product_type;
  if (product.vendor) specs.vendor = product.vendor;
  if (product.weight) specs.weight = product.weight;
  
  // 從變體中提取規格
  if (product.variants && product.variants.length > 0) {
    const variant = product.variants[0];
    if (variant.option1) specs.option1 = variant.option1;
    if (variant.option2) specs.option2 = variant.option2;
    if (variant.option3) specs.option3 = variant.option3;
  }
  
  return Object.keys(specs).length > 0 ? specs : null;
};

/**
 * 提取商品尺寸信息
 */
const extractDimensions = (product) => {
  // 這裡可以根據實際的EasyStore數據結構來提取尺寸信息
  // 目前返回null，後續可以根據實際數據調整
  return null;
};

/**
 * 保存商品到數據庫
 */
const saveProductToDatabase = async (productData) => {
  try {
    console.log('正在保存商品到數據庫...');
    
    const [product, created] = await ProductInfo.findOrCreate({
      where: { productId: productData.productId },
      defaults: productData
    });
    
    if (!created) {
      // 如果商品已存在，更新數據
      await product.update(productData);
      console.log('✅ 商品數據已更新');
    } else {
      console.log('✅ 新商品已保存');
    }
    
    return product;
    
  } catch (error) {
    console.error('❌ 保存商品到數據庫失敗:', error);
    throw error;
  }
};

/**
 * 同步單個商品
 */
const syncSingleProduct = async (productId) => {
  try {
    console.log(`開始同步商品: ${productId}`);
    
    // 1. 從EasyStore獲取商品數據
    const easyStoreProduct = await fetchProductFromEasyStore(productId);
    
    // 2. 轉換數據格式
    const transformedProduct = transformProductData(easyStoreProduct);
    
    // 3. 保存到數據庫
    const savedProduct = await saveProductToDatabase(transformedProduct);
    
    console.log('✅ 商品同步完成');
    return savedProduct;
    
  } catch (error) {
    console.error(`❌ 同步商品 ${productId} 失敗:`, error);
    throw error;
  }
};

module.exports = {
  fetchProductFromEasyStore,
  transformProductData,
  saveProductToDatabase,
  syncSingleProduct
};
