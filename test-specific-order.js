const axios = require('axios');
require('dotenv').config();

/**
 * 測試特定訂單ID的查詢
 * 您的原始 URL: https://api.easystore.co/api/3.0/orders.json?query=18506
 * 這可能是在查詢訂單ID為 18506 的特定訂單
 */

async function testSpecificOrder() {
  const apiKey = process.env.EASYSTORE_API_KEY;
  const orderId = '18506';
  
  console.log('=== 測試特定訂單查詢 ===');
  console.log('API Key:', apiKey ? `${apiKey.substring(0, 8)}...` : '未設置');
  console.log('訂單ID:', orderId);
  
  // 測試不同的訂單查詢格式
  const orderQueryFormats = [
    // 原始格式
    `https://api.easystore.co/api/3.0/orders.json?query=${orderId}`,
    
    // 可能的其他格式
    `https://api.easystore.co/api/3.0/orders/${orderId}`,
    `https://api.easystore.co/api/3.0/orders/${orderId}.json`,
    `https://api.easystore.co/api/3.0/order/${orderId}`,
    `https://api.easystore.co/api/3.0/order/${orderId}.json`,
    
    // 使用不同的查詢參數
    `https://api.easystore.co/api/3.0/orders?id=${orderId}`,
    `https://api.easystore.co/api/3.0/orders?order_id=${orderId}`,
    `https://api.easystore.co/api/3.0/orders?order_number=${orderId}`,
    
    // 嘗試不帶 .json 擴展名
    `https://api.easystore.co/api/3.0/orders?query=${orderId}`,
  ];
  
  for (let i = 0; i < orderQueryFormats.length; i++) {
    const url = orderQueryFormats[i];
    console.log(`\n--- 測試格式 ${i + 1}: ${url} ---`);
    
    // 嘗試不同的認證方式
    const authMethods = [
      {
        name: 'Bearer Token',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      },
      {
        name: 'API Key Header',
        headers: {
          'X-API-Key': apiKey,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      },
      {
        name: 'Query Parameter',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        url: url + (url.includes('?') ? '&' : '?') + `api_key=${apiKey}`
      }
    ];
    
    for (const method of authMethods) {
      const requestUrl = method.url || url;
      console.log(`  ${method.name}: ${requestUrl}`);
      
      try {
        const response = await axios.get(requestUrl, {
          headers: method.headers,
          timeout: 10000
        });
        
        console.log('  ✅ 成功!');
        console.log('  狀態碼:', response.status);
        console.log('  響應數據:');
        console.log(JSON.stringify(response.data, null, 2));
        
        // 如果成功，保存配置並測試數據庫
        console.log('\n🎉 找到正確的配置!');
        console.log('URL:', requestUrl);
        console.log('Headers:', method.headers);
        
        // 測試數據庫保存
        if (response.data) {
          await testDatabaseSave(response.data, requestUrl, method.headers);
        }
        
        return { url: requestUrl, headers: method.headers, data: response.data };
        
      } catch (error) {
        const status = error.response?.status;
        const message = error.response?.data?.error?.message || error.message;
        console.log(`  ❌ 失敗 (${status}): ${message}`);
        
        // 提供更詳細的錯誤信息
        if (status === 401) {
          console.log('  ⚠️ 認證失敗 - 檢查 API Key');
        } else if (status === 403) {
          console.log('  ⚠️ 權限不足 - 檢查 API Key 權限');
        } else if (status === 404) {
          console.log('  ⚠️ 端點不存在或訂單不存在');
        }
      }
    }
  }
  
  console.log('\n=== 所有測試完成 ===');
  console.log('建議：');
  console.log('1. 檢查 EasyStore 後台的 API 設置');
  console.log('2. 確認訂單ID 18506 是否存在');
  console.log('3. 檢查 API Key 是否有正確的權限');
  console.log('4. 聯繫 EasyStore 技術支持確認 API 格式');
  
  return null;
}

/**
 * 測試數據庫保存功能
 */
async function testDatabaseSave(orderData, successUrl, successHeaders) {
  console.log('\n=== 測試數據庫保存 ===');
  
  try {
    const sequelize = require('./src/config/database');
    const Order = require('./src/models/Order');
    
    // 測試數據庫連接
    console.log('正在測試數據庫連接...');
    await sequelize.authenticate();
    console.log('✅ 數據庫連接成功');
    
    // 同步數據庫模型
    console.log('正在同步數據庫模型...');
    await sequelize.sync();
    console.log('✅ 數據庫模型同步成功');
    
    // 處理不同的數據格式
    let orderToProcess;
    
    if (Array.isArray(orderData)) {
      orderToProcess = orderData[0];
      console.log('數據是數組格式，處理第一個項目');
    } else if (orderData.order) {
      orderToProcess = orderData.order;
      console.log('數據包含 order 字段');
    } else if (orderData.orders && orderData.orders.length > 0) {
      orderToProcess = orderData.orders[0];
      console.log('數據包含 orders 數組，處理第一個項目');
    } else {
      orderToProcess = orderData;
      console.log('直接處理數據');
    }
    
    console.log('處理的訂單數據結構:');
    console.log(JSON.stringify(orderToProcess, null, 2));
    
    // 轉換為我們的數據格式
    const orderToSave = {
      orderNumber: orderToProcess.order_number || orderToProcess.id?.toString() || 'UNKNOWN',
      customerName: orderToProcess.customer ? 
        `${orderToProcess.customer.first_name || ''} ${orderToProcess.customer.last_name || ''}`.trim() || '未知客戶' : '未知客戶',
      customerEmail: orderToProcess.customer?.email || null,
      customerPhone: orderToProcess.customer?.phone || null,
      orderDate: orderToProcess.created_at ? new Date(orderToProcess.created_at) : new Date(),
      status: orderToProcess.status || 'pending',
      totalAmount: parseFloat(orderToProcess.total_price || orderToProcess.total || 0),
      items: orderToProcess.line_items || orderToProcess.items || [],
      shippingAddress: orderToProcess.shipping_address || {},
      billingAddress: orderToProcess.billing_address || null,
      paymentMethod: orderToProcess.payment_method || null,
      paymentStatus: orderToProcess.payment_status || 'pending',
      notes: orderToProcess.notes || null,
      easyStoreOrderId: orderToProcess.id?.toString() || orderToProcess.order_number || 'UNKNOWN'
    };
    
    console.log('\n準備保存的訂單數據:');
    console.log(JSON.stringify(orderToSave, null, 2));
    
    // 保存到數據庫
    const [savedOrder, created] = await Order.findOrCreate({
      where: { easyStoreOrderId: orderToSave.easyStoreOrderId },
      defaults: orderToSave
    });
    
    if (created) {
      console.log('\n✅ 訂單成功保存到數據庫');
    } else {
      console.log('\n⚠️ 訂單已存在，正在更新...');
      await savedOrder.update(orderToSave);
      console.log('✅ 訂單更新成功');
    }
    
    console.log('保存的訂單數據庫ID:', savedOrder.id);
    console.log('EasyStore 訂單ID:', savedOrder.easyStoreOrderId);
    
    // 保存成功的配置信息
    console.log('\n=== 成功配置信息 ===');
    console.log('API URL:', successUrl);
    console.log('Headers:', JSON.stringify(successHeaders, null, 2));
    
  } catch (error) {
    console.error('\n❌ 數據庫操作失敗:', error.message);
    console.error('詳細錯誤:', error);
  }
}

// 執行測試
testSpecificOrder().catch(console.error);
