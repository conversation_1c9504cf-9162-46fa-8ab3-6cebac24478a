// 測試修復後的商品匯入功能
const testFixedImport = async () => {
  console.log('=== 測試修復後的商品匯入功能 ===');
  
  // 測試商品 - 包含重複的 UPC
  const testProducts = [
    {
      productName: 'The Beatles - Abbey Road (Remastered)',
      upc: '094638241614', // 這個 UPC 已經存在於數據庫中
      sku: 'VINYL-BEATLES-AR-RM-NEW',
      artist: 'The Beatles',
      brand: 'Apple Records',
      supplier: 'AMI',
      category: 'Vinyl Records',
      price: 950.00,
      expectedQuantity: 5,
      actualQuantity: 5,
      status: 'verified',
      specifications: {
        format: 'LP',
        speed: '33 RPM',
        size: '12 inch',
        edition: 'Remastered'
      },
      tags: ['經典', '重製版', '代購']
    },
    {
      productName: 'Queen - Bohemian Rhapsody (Limited Edition)',
      upc: '050087349721', // 新的 UPC
      sku: 'VINYL-QUEEN-BR-LE',
      artist: 'Queen',
      brand: 'Hollywood Records',
      supplier: 'MONO',
      category: 'Vinyl Records',
      price: 1200.00,
      expectedQuantity: 3,
      actualQuantity: 3,
      status: 'verified',
      specifications: {
        format: 'LP',
        speed: '33 RPM',
        size: '12 inch',
        edition: 'Limited'
      },
      tags: ['限量版', '經典', '搖滾']
    },
    {
      productName: 'David Bowie - The Rise and Fall of Ziggy Stardust',
      upc: '190295851927', // 新的 UPC
      sku: 'VINYL-BOWIE-ZS',
      artist: 'David Bowie',
      brand: 'Parlophone',
      supplier: 'SK1',
      category: 'Vinyl Records',
      price: 850.00,
      expectedQuantity: 4,
      actualQuantity: 2, // 數量不符
      status: 'discrepancy',
      specifications: {
        format: 'LP',
        speed: '33 RPM',
        size: '12 inch'
      },
      tags: ['經典', '搖滾', '藝術搖滾']
    }
  ];
  
  try {
    console.log('\n1. 檢查匯入前的數據庫狀態...');
    const beforeResponse = await fetch('http://localhost:5000/api/products');
    const beforeProducts = await beforeResponse.json();
    console.log(`匯入前數據庫中有 ${beforeProducts.length} 個商品`);
    
    // 查找是否已存在 Abbey Road
    const existingAbbeyRoad = beforeProducts.find(p => p.upc === '094638241614');
    if (existingAbbeyRoad) {
      console.log(`✅ 找到現有的 Abbey Road 商品，庫存: ${existingAbbeyRoad.stockQuantity}`);
    }
    
    console.log('\n2. 測試匯入已檢驗商品（包含重複 UPC）...');
    const verifiedProducts = testProducts.filter(p => p.status === 'verified');
    
    const importResponse = await fetch('http://localhost:5000/api/products/import', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        products: verifiedProducts, 
        type: 'verified' 
      })
    });
    
    const importResult = await importResponse.json();
    console.log('匯入結果:', importResult);
    
    console.log('\n3. 檢查匯入後的數據庫狀態...');
    const afterResponse = await fetch('http://localhost:5000/api/products');
    const afterProducts = await afterResponse.json();
    console.log(`匯入後數據庫中有 ${afterProducts.length} 個商品`);
    
    // 檢查 Abbey Road 是否正確合併
    const updatedAbbeyRoad = afterProducts.find(p => p.upc === '094638241614');
    if (updatedAbbeyRoad) {
      console.log(`✅ Abbey Road 商品已更新:`);
      console.log(`   - 商品名稱: ${updatedAbbeyRoad.productName}`);
      console.log(`   - 庫存數量: ${updatedAbbeyRoad.stockQuantity}`);
      console.log(`   - 上架狀態: ${updatedAbbeyRoad.isPublished ? '已上架' : '未上架'}`);
    }
    
    // 檢查新商品
    const newQueen = afterProducts.find(p => p.upc === '050087349721');
    if (newQueen) {
      console.log(`✅ 新增 Queen 商品:`);
      console.log(`   - 商品名稱: ${newQueen.productName}`);
      console.log(`   - 庫存數量: ${newQueen.stockQuantity}`);
      console.log(`   - 上架狀態: ${newQueen.isPublished ? '已上架' : '未上架'}`);
    }
    
    console.log('\n4. 測試匯入所有商品（包含未檢驗）...');
    const unverifiedProducts = testProducts.filter(p => p.status !== 'verified');
    
    const allImportResponse = await fetch('http://localhost:5000/api/products/import', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        products: unverifiedProducts, 
        type: 'all' 
      })
    });
    
    const allImportResult = await allImportResponse.json();
    console.log('所有商品匯入結果:', allImportResult);
    
    console.log('\n5. 最終數據庫狀態檢查...');
    const finalResponse = await fetch('http://localhost:5000/api/products');
    const finalProducts = await finalResponse.json();
    console.log(`最終數據庫中有 ${finalProducts.length} 個商品:`);
    
    finalProducts.forEach((product, index) => {
      console.log(`${index + 1}. ${product.productName}`);
      console.log(`   - UPC: ${product.upc}`);
      console.log(`   - 庫存: ${product.stockQuantity}`);
      console.log(`   - 上架: ${product.isPublished ? '是' : '否'}`);
      console.log('');
    });
    
    console.log('✅ 商品匯入功能測試完成');
    
  } catch (error) {
    console.error('❌ 測試失敗:', error);
  }
};

// 執行測試
testFixedImport();
