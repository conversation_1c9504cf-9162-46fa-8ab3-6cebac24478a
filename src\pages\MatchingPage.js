import { useState, useEffect } from 'react';
import { 
  Table, 
  Card, 
  Row, 
  Col, 
  Button, 
  message, 
  Spin, 
  Tag,
  Space,
  Typography,
  Statistic,
  Modal,
  Collapse,
  Progress,
  Divider,
  Badge,
  Tooltip
} from 'antd';
import { 
  SwapOutlined,
  DatabaseOutlined,
  ShoppingCartOutlined,
  PlayCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { Panel } = Collapse;

function MatchingPage() {
  const [loading, setLoading] = useState(false);
  const [products, setProducts] = useState([]);
  const [orders, setOrders] = useState([]);
  const [matchingResults, setMatchingResults] = useState([]);
  const [allocationResults, setAllocationResults] = useState([]);
  const [allocatedItems, setAllocatedItems] = useState(new Set()); // 追蹤已分派的項目
  const [isMatching, setIsMatching] = useState(false);
  const [matchingModalVisible, setMatchingModalVisible] = useState(false);

  // 載入所有商品
  const loadProducts = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:5000/api/products');
      const data = await response.json();
      
      setProducts(data);
      message.success(`成功載入 ${data.length} 個商品`);
    } catch (error) {
      console.error('載入商品失敗:', error);
      message.error('載入商品失敗');
    } finally {
      setLoading(false);
    }
  };

  // 載入所有訂單
  const loadOrders = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:5000/api/orders');
      const data = await response.json();
      
      setOrders(data);
      message.success(`成功載入 ${data.length} 筆訂單`);
    } catch (error) {
      console.error('載入訂單失敗:', error);
      message.error('載入訂單失敗');
    } finally {
      setLoading(false);
    }
  };

  // SKU 模糊匹配函數（忽略數字編號）
  const fuzzyMatchSKU = (orderSKU, productSKU) => {
    if (!orderSKU || !productSKU) return false;
    
    // 移除數字編號（如 -001, -01 等）
    const cleanOrderSKU = orderSKU.replace(/-\d+$/, '').toLowerCase();
    const cleanProductSKU = productSKU.replace(/-\d+$/, '').toLowerCase();
    
    return cleanOrderSKU === cleanProductSKU;
  };

  // 商品名稱模糊匹配函數
  const fuzzyMatchName = (orderName, productName) => {
    if (!orderName || !productName) return false;
    
    // 轉換為小寫並移除多餘空格
    const cleanOrderName = orderName.toLowerCase().trim();
    const cleanProductName = productName.toLowerCase().trim();
    
    // 檢查是否包含主要關鍵字
    return cleanOrderName === cleanProductName || 
           cleanOrderName.includes(cleanProductName) || 
           cleanProductName.includes(cleanOrderName);
  };

  // 開始商品比對和分派
  const startMatching = async () => {
    if (products.length === 0) {
      message.warning('請先載入商品資料');
      return;
    }
    
    if (orders.length === 0) {
      message.warning('請先載入訂單資料');
      return;
    }

    setIsMatching(true);
    setMatchingModalVisible(true);
    
    try {
      // 1. 收集所有訂單項目並按訂購日期排序
      const allOrderItems = [];

      orders.forEach(order => {
        if (order.items && Array.isArray(order.items)) {
          order.items.forEach((item, itemIndex) => {
            // 創建唯一的項目ID來追蹤分派狀態
            const itemKey = `${order.id}-${itemIndex}-${item.name}-${item.sku}`;

            // 只處理未分派的項目
            if (!allocatedItems.has(itemKey)) {
              allOrderItems.push({
                ...item,
                itemKey, // 添加唯一標識
                orderId: order.id,
                orderNumber: order.orderNumber,
                customerName: order.customerName,
                orderDate: new Date(order.orderDate),
                originalOrder: order
              });
            }
          });
        }
      });

      // 按訂購日期排序（最早的優先）
      allOrderItems.sort((a, b) => a.orderDate - b.orderDate);

      console.log(`待分派項目數量: ${allOrderItems.length}`);
      console.log(`已分派項目數量: ${allocatedItems.size}`);
      
      console.log('所有訂單項目（按日期排序）:', allOrderItems);
      
      // 2. 開始匹配和分派
      const results = [];
      const productStock = {}; // 追蹤商品庫存
      
      // 初始化庫存追蹤
      products.forEach(product => {
        productStock[product.id] = product.stockQuantity;
      });
      
      for (const orderItem of allOrderItems) {
        const matchResult = {
          orderItem,
          matchedProduct: null,
          allocated: false,
          reason: ''
        };
        
        // 尋找匹配的商品
        const matchedProduct = products.find(product => {
          const nameMatch = fuzzyMatchName(orderItem.name, product.productName);
          const skuMatch = fuzzyMatchSKU(orderItem.sku, product.sku);
          
          return nameMatch && skuMatch;
        });
        
        if (matchedProduct) {
          matchResult.matchedProduct = matchedProduct;
          
          // 檢查庫存
          const availableStock = productStock[matchedProduct.id] || 0;
          const requiredQuantity = orderItem.quantity || 1;
          
          if (availableStock >= requiredQuantity) {
            // 分派成功
            matchResult.allocated = true;
            matchResult.reason = `成功分派 ${requiredQuantity} 個商品`;

            // 扣減庫存
            productStock[matchedProduct.id] -= requiredQuantity;

            // 標記為已分派
            matchResult.itemKey = orderItem.itemKey;
          } else {
            matchResult.reason = `庫存不足（需要 ${requiredQuantity}，剩餘 ${availableStock}）`;
          }
        } else {
          matchResult.reason = '找不到匹配的商品';
        }
        
        results.push(matchResult);
      }
      
      setAllocationResults(results);
      
      // 3. 更新數據庫庫存
      const allocations = results
        .filter(r => r.allocated)
        .map(r => ({
          productId: r.matchedProduct.id,
          quantity: r.orderItem.quantity || 1,
          orderId: r.orderItem.orderId,
          orderItemId: r.orderItem.id
        }));

      if (allocations.length > 0) {
        try {
          const response = await fetch('http://localhost:5000/api/products/allocate', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ allocations })
          });

          const allocationResult = await response.json();

          if (allocationResult.success) {
            console.log('庫存更新成功:', allocationResult);

            // 更新已分派項目集合
            const newAllocatedItems = new Set(allocatedItems);
            results.filter(r => r.allocated).forEach(r => {
              if (r.itemKey) {
                newAllocatedItems.add(r.itemKey);
              }
            });
            setAllocatedItems(newAllocatedItems);

            // 重新載入商品數據以顯示最新庫存
            await loadProducts();

            message.success(
              `商品分派完成！成功分派 ${allocationResult.successCount} 個項目，失敗 ${allocationResult.failCount} 個項目`
            );
          } else {
            throw new Error(allocationResult.message);
          }
        } catch (apiError) {
          console.error('更新庫存失敗:', apiError);
          message.error(`分派完成但庫存更新失敗：${apiError.message}`);
        }
      } else {
        message.warning('沒有成功分派的項目');
      }
      
    } catch (error) {
      console.error('商品分派失敗:', error);
      message.error('商品分派失敗');
    } finally {
      setIsMatching(false);
    }
  };

  // 計算統計數據
  const getStats = () => {
    const totalProducts = products.length;
    const totalStock = products.reduce((sum, p) => sum + (p.stockQuantity || 0), 0);
    const totalOrders = orders.length;
    const totalOrderItems = orders.reduce((sum, o) => sum + (o.items?.length || 0), 0);

    // 使用已分派項目集合的大小
    const allocatedItemsCount = allocatedItems.size;
    const allocationRate = totalOrderItems > 0 ? (allocatedItemsCount / totalOrderItems * 100) : 0;

    return {
      totalProducts,
      totalStock,
      totalOrders,
      totalOrderItems,
      allocatedItems: allocatedItemsCount,
      allocationRate
    };
  };

  const stats = getStats();

  // 商品表格列定義
  const productColumns = [
    {
      title: '商品名稱',
      dataIndex: 'productName',
      key: 'productName',
      width: 300,
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text}</div>
          <div style={{ color: '#666', fontSize: '12px' }}>
            SKU: {record.sku} | UPC: {record.upc}
          </div>
        </div>
      ),
    },
    {
      title: '庫存',
      dataIndex: 'stockQuantity',
      key: 'stockQuantity',
      width: 80,
      align: 'center',
      render: (quantity) => (
        <Tag color={quantity > 0 ? 'green' : 'red'}>
          {quantity}
        </Tag>
      ),
    },
    {
      title: '品牌',
      dataIndex: 'brand',
      key: 'brand',
      width: 120,
    },
    {
      title: '供應商',
      dataIndex: 'supplier',
      key: 'supplier',
      width: 100,
    },
    {
      title: '狀態',
      dataIndex: 'isPublished',
      key: 'isPublished',
      width: 80,
      align: 'center',
      render: (isPublished) => (
        <Tag color={isPublished ? 'green' : 'orange'}>
          {isPublished ? '已上架' : '未上架'}
        </Tag>
      ),
    },
  ];

  // 訂單表格列定義
  const orderColumns = [
    {
      title: '訂單編號',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      width: 120,
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text}</div>
          <div style={{ color: '#666', fontSize: '12px' }}>
            {new Date(record.orderDate).toLocaleDateString('zh-TW')}
          </div>
        </div>
      ),
    },
    {
      title: '客戶名稱',
      dataIndex: 'customerName',
      key: 'customerName',
      width: 120,
    },
    {
      title: '商品項目',
      key: 'items',
      render: (_, record) => {
        const items = record.items || [];
        // 計算該訂單中已分派的項目數量
        const allocatedItemsCount = items.filter((item, itemIndex) => {
          const itemKey = `${record.id}-${itemIndex}-${item.name}-${item.sku}`;
          return allocatedItems.has(itemKey);
        }).length;
        const totalItems = items.length;
        const isCompleted = allocatedItemsCount === totalItems && totalItems > 0;

        return (
          <div>
            <Badge
              count={totalItems}
              style={{ backgroundColor: isCompleted ? '#52c41a' : '#1890ff' }}
            >
              <Button
                size="small"
                type={isCompleted ? "primary" : "default"}
                style={{
                  backgroundColor: isCompleted ? '#f6ffed' : 'white',
                  borderColor: isCompleted ? '#52c41a' : '#d9d9d9'
                }}
              >
                查看商品
              </Button>
            </Badge>
            <div style={{ fontSize: '12px', marginTop: '4px' }}>
              已分派: {allocatedItemsCount}/{totalItems}
            </div>
          </div>
        );
      },
    },
    {
      title: '總金額',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      width: 100,
      align: 'right',
      render: (amount) => `NT$${amount?.toLocaleString() || 0}`,
    },
    {
      title: '狀態',
      key: 'status',
      width: 100,
      align: 'center',
      render: (_, record) => {
        const items = record.items || [];
        // 計算該訂單中已分派的項目數量
        const allocatedItemsCount = items.filter((item, itemIndex) => {
          const itemKey = `${record.id}-${itemIndex}-${item.name}-${item.sku}`;
          return allocatedItems.has(itemKey);
        }).length;
        const totalItems = items.length;
        const isCompleted = allocatedItemsCount === totalItems && totalItems > 0;

        return (
          <Tag color={isCompleted ? 'green' : 'orange'}>
            {isCompleted ? '已完成' : '待分派'}
          </Tag>
        );
      },
    },
  ];

  // 訂單展開內容
  const expandedRowRender = (record) => {
    const items = record.items || [];

    const itemColumns = [
      {
        title: '商品名稱',
        dataIndex: 'name',
        key: 'name',
        width: 300,
      },
      {
        title: 'SKU',
        dataIndex: 'sku',
        key: 'sku',
        width: 150,
      },
      {
        title: '數量',
        dataIndex: 'quantity',
        key: 'quantity',
        width: 80,
        align: 'center',
      },
      {
        title: '單價',
        dataIndex: 'price',
        key: 'price',
        width: 100,
        align: 'right',
        render: (price) => `NT$${price?.toLocaleString() || 0}`,
      },
      {
        title: '分派狀態',
        key: 'allocationStatus',
        width: 120,
        align: 'center',
        render: (_, item, itemIndex) => {
          const itemKey = `${record.id}-${itemIndex}-${item.name}-${item.sku}`;
          const isAllocated = allocatedItems.has(itemKey);

          if (isAllocated) {
            return (
              <Tooltip title="已成功分派">
                <Tag color="green">已分派</Tag>
              </Tooltip>
            );
          }

          // 檢查是否在最近的分派結果中失敗
          const recentAllocation = allocationResults.find(r =>
            r.orderItem.orderId === record.id &&
            r.orderItem.name === item.name &&
            r.orderItem.sku === item.sku
          );

          if (recentAllocation && !recentAllocation.allocated) {
            return (
              <Tooltip title={recentAllocation.reason}>
                <Tag color="red">失敗</Tag>
              </Tooltip>
            );
          }

          return <Tag color="default">未處理</Tag>;
        },
      },
    ];

    return (
      <Table
        columns={itemColumns}
        dataSource={items}
        pagination={false}
        size="small"
        rowKey={(item, index) => `${record.id}-${index}`}
        rowClassName={(item, itemIndex) => {
          const itemKey = `${record.id}-${itemIndex}-${item.name}-${item.sku}`;
          return allocatedItems.has(itemKey) ? 'row-allocated' : '';
        }}
      />
    );
  };

  return (
    <div style={{ padding: '0 24px' }}>
      {/* 頁面標題 */}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0 }}>商品比對</Title>
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={async () => {
              try {
                // 調用後端API重置分派狀態
                const response = await fetch('http://localhost:5000/api/products/reset-allocations', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' }
                });

                const result = await response.json();

                if (result.success) {
                  setAllocatedItems(new Set());
                  setAllocationResults([]);
                  message.success('已重置分派狀態');
                } else {
                  throw new Error(result.message);
                }
              } catch (error) {
                console.error('重置分派狀態失敗:', error);
                message.error('重置分派狀態失敗');
              }
            }}
            disabled={allocatedItems.size === 0}
          >
            重置分派狀態
          </Button>
          <Button
            icon={<PlayCircleOutlined />}
            type="primary"
            size="large"
            onClick={startMatching}
            loading={isMatching}
            disabled={products.length === 0 || orders.length === 0}
          >
            開始分派產品
          </Button>
        </Space>
      </div>

      {/* 統計資訊 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={4}>
          <Card size="small">
            <Statistic
              title="商品總數"
              value={stats.totalProducts}
              prefix={<DatabaseOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card size="small">
            <Statistic
              title="總庫存"
              value={stats.totalStock}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card size="small">
            <Statistic
              title="訂單總數"
              value={stats.totalOrders}
              prefix={<ShoppingCartOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card size="small">
            <Statistic
              title="訂單項目"
              value={stats.totalOrderItems}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card size="small">
            <Statistic
              title="已分派項目"
              value={stats.allocatedItems}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card size="small">
            <Statistic
              title="分派成功率"
              value={stats.allocationRate}
              precision={1}
              suffix="%"
              valueStyle={{ color: stats.allocationRate > 80 ? '#52c41a' : '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要內容區域 */}
      <Row gutter={16}>
        {/* 左側：商品清單 */}
        <Col span={12}>
          <Card
            title="所有商品"
            size="small"
            extra={
              <Space>
                <Button
                  icon={<DatabaseOutlined />}
                  onClick={loadProducts}
                  loading={loading}
                >
                  匯入所有商品
                </Button>
                <Badge count={products.length} showZero>
                  <span>商品數量</span>
                </Badge>
              </Space>
            }
          >
            <Table
              columns={productColumns}
              dataSource={products}
              rowKey="id"
              size="small"
              pagination={{
                pageSize: 10,
                showSizeChanger: false,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 項，共 ${total} 項`
              }}
              scroll={{ y: 400 }}
            />
          </Card>
        </Col>

        {/* 右側：訂單清單 */}
        <Col span={12}>
          <Card
            title="客戶訂單"
            size="small"
            extra={
              <Space>
                <Button
                  icon={<ShoppingCartOutlined />}
                  onClick={loadOrders}
                  loading={loading}
                >
                  匯入訂單
                </Button>
                <Badge count={orders.length} showZero>
                  <span>訂單數量</span>
                </Badge>
              </Space>
            }
          >
            <Table
              columns={orderColumns}
              dataSource={orders}
              rowKey="id"
              size="small"
              expandable={{
                expandedRowRender,
                expandRowByClick: true,
                expandIcon: ({ expanded, onExpand, record }) => (
                  <Button
                    type="text"
                    size="small"
                    icon={expanded ? <ExclamationCircleOutlined /> : <InfoCircleOutlined />}
                    onClick={e => onExpand(record, e)}
                  />
                )
              }}
              pagination={{
                pageSize: 8,
                showSizeChanger: false,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 項，共 ${total} 項`
              }}
              scroll={{ x: 800, y: 400 }}
            />
          </Card>
        </Col>
      </Row>

      {/* 分派進度模態框 */}
      <Modal
        title="商品分派進度"
        open={matchingModalVisible}
        onCancel={() => setMatchingModalVisible(false)}
        footer={null}
        width={800}
      >
        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          {isMatching ? (
            <div>
              <Spin size="large" />
              <div style={{ marginTop: 16 }}>
                <Text>正在進行商品比對和分派...</Text>
              </div>
            </div>
          ) : (
            <div>
              <CheckCircleOutlined style={{ fontSize: 48, color: '#52c41a' }} />
              <div style={{ marginTop: 16 }}>
                <Title level={4}>分派完成！</Title>
                <Text>
                  成功分派 {stats.allocatedItems} / {stats.totalOrderItems} 個項目
                </Text>
              </div>
            </div>
          )}
        </div>
      </Modal>

      {/* 自定義樣式 */}
      <style jsx>{`
        .row-allocated {
          background-color: #f6ffed !important;
        }
        .ant-table-expanded-row > td {
          background-color: #fafafa;
        }
      `}</style>
    </div>
  );
}

export default MatchingPage;
