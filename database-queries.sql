-- ===================================
-- PostgreSQL 數據庫檢查 SQL 查詢
-- ===================================

-- 1. 檢查所有數據庫
SELECT datname AS database_name 
FROM pg_database 
WHERE datistemplate = false
ORDER BY datname;

-- 2. 檢查當前數據庫中的所有表
SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY tablename;

-- 3. 檢查 orders 表的結構（如果存在）
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'orders' 
    AND table_schema = 'public'
ORDER BY ordinal_position;

-- 4. 檢查 orders 表中的數據（如果存在）
-- SELECT * FROM orders ORDER BY "createdAt" DESC;

-- 5. 檢查表的索引
SELECT
    indexname,
    indexdef
FROM pg_indexes
WHERE tablename = 'orders'
    AND schemaname = 'public';

-- 6. 檢查數據庫大小
SELECT 
    pg_database.datname AS database_name,
    pg_size_pretty(pg_database_size(pg_database.datname)) AS size
FROM pg_database
WHERE datname = 'UniversalVinyl_MIS';

-- 7. 檢查當前連接的數據庫
SELECT current_database();

-- 8. 檢查所有序列（用於自動遞增ID）
SELECT 
    schemaname,
    sequencename,
    last_value
FROM pg_sequences
WHERE schemaname = 'public';
