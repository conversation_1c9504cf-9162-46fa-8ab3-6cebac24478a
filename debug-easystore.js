const axios = require('axios');
require('dotenv').config();

/**
 * 調試 EasyStore API 連接
 * 根據 EasyStore 官方文檔，API 格式通常是：
 * https://{store_name}.api.easystore.co/api/3.0/orders
 * 或者
 * https://api.easystore.co/api/3.0/{store_id}/orders
 */

async function debugEasyStoreAPI() {
  const apiKey = process.env.EASYSTORE_API_KEY;
  
  console.log('=== EasyStore API 調試信息 ===');
  console.log('API Key:', apiKey ? `${apiKey.substring(0, 8)}...` : '未設置');
  console.log('原始 URL:', process.env.EASYSTORE_API_URL);
  
  // 從原始 URL 中提取可能的信息
  const originalUrl = process.env.EASYSTORE_API_URL;
  const queryMatch = originalUrl.match(/query=(\d+)/);
  const possibleStoreId = queryMatch ? queryMatch[1] : null;
  
  console.log('可能的商店ID:', possibleStoreId);
  
  // 嘗試不同的 API 格式
  const testUrls = [
    // 原始 URL
    originalUrl,
    
    // 如果 query 參數實際上是商店ID
    possibleStoreId ? `https://api.easystore.co/api/3.0/${possibleStoreId}/orders` : null,
    
    // 嘗試不同的端點
    `https://api.easystore.co/api/3.0/orders`,
    `https://api.easystore.co/v3/orders`,
    
    // 如果需要在 header 中指定商店
    possibleStoreId ? `https://api.easystore.co/api/3.0/orders` : null,
  ].filter(Boolean);
  
  for (let i = 0; i < testUrls.length; i++) {
    const url = testUrls[i];
    console.log(`\n--- 測試 ${i + 1}: ${url} ---`);
    
    try {
      // 嘗試不同的 header 組合
      const headerOptions = [
        {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'X-Store-Id': possibleStoreId
        },
        {
          'X-API-Key': apiKey,
          'Content-Type': 'application/json'
        }
      ];
      
      for (let j = 0; j < headerOptions.length; j++) {
        const headers = headerOptions[j];
        console.log(`  嘗試 header 組合 ${j + 1}:`, Object.keys(headers));
        
        try {
          const response = await axios.get(url, {
            headers,
            timeout: 10000
          });
          
          console.log('  ✅ 成功!');
          console.log('  狀態碼:', response.status);
          console.log('  響應數據:');
          console.log(JSON.stringify(response.data, null, 2));
          
          // 如果成功，保存正確的配置
          console.log('\n=== 找到正確的配置 ===');
          console.log('URL:', url);
          console.log('Headers:', headers);
          
          return { url, headers, data: response.data };
          
        } catch (headerError) {
          console.log(`  ❌ Header 組合 ${j + 1} 失敗:`, headerError.response?.status || headerError.message);
        }
      }
      
    } catch (error) {
      console.log('❌ URL 失敗:', error.response?.status || error.message);
    }
  }
  
  console.log('\n=== 所有嘗試都失敗了 ===');
  console.log('建議檢查：');
  console.log('1. 確認您的 EasyStore 商店名稱或 ID');
  console.log('2. 檢查 API Key 是否有正確的權限');
  console.log('3. 確認 API 端點格式');
  console.log('4. 檢查是否需要特殊的認證方式');
  
  // 嘗試獲取 API 文檔或基本信息
  console.log('\n=== 嘗試獲取 API 基本信息 ===');
  try {
    const response = await axios.get('https://api.easystore.co', {
      timeout: 5000
    });
    console.log('API 根端點響應:', response.status);
    console.log('響應數據:', response.data);
  } catch (error) {
    console.log('無法獲取 API 基本信息:', error.message);
  }
}

// 執行調試
debugEasyStoreAPI().catch(console.error);
