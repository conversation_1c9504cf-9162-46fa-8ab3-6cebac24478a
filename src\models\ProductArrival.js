const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const ProductArrival = sequelize.define('ProductArrival', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  supplierName: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: '供應商名稱'
  },
  arrivalBatchId: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: '到貨批次ID'
  },
  productName: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: '商品名稱'
  },
  upc: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: '商品UPC條碼'
  },
  artist: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '藝術家/歌手'
  },
  expectedQuantity: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '預期數量（出貨單上的數量）'
  },
  actualQuantity: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '實際掃碼數量'
  },
  specifications: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: '商品規格（顏色、版本等）'
  },
  productImage: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '商品圖片URL'
  },
  status: {
    type: DataTypes.ENUM('pending', 'checking', 'completed', 'discrepancy'),
    allowNull: false,
    defaultValue: 'pending',
    comment: '檢驗狀態：待檢驗、檢驗中、已完成、有差異'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '備註'
  },
  lastScannedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '最後掃碼時間'
  },
  arrivalDate: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: '到貨日期'
  }
}, {
  tableName: 'product_arrivals',
  timestamps: true,
  indexes: [
    {
      fields: ['upc']
    },
    {
      fields: ['supplierName', 'arrivalBatchId']
    },
    {
      fields: ['status']
    }
  ]
});

module.exports = ProductArrival;
