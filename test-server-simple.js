const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = 5000;

// 中間件
app.use(cors());
app.use(express.json());

// 提供靜態檔案服務 (圖片)
app.use('/images', express.static(path.join(__dirname, 'public', 'images')));

// 獲取所有商品
app.get('/api/products', (req, res) => {
  try {
    console.log('收到獲取商品請求');
    
    // 讀取模擬資料
    const mockDataPath = path.join(__dirname, 'src', 'data', 'mockProducts.json');
    if (fs.existsSync(mockDataPath)) {
      const mockData = JSON.parse(fs.readFileSync(mockDataPath, 'utf8'));
      console.log(`返回 ${mockData.length} 個商品`);
      res.json(mockData);
    } else {
      console.log('模擬資料檔案不存在');
      res.json([]);
    }
    
  } catch (error) {
    console.error('獲取商品數據失敗:', error);
    res.status(500).json({ 
      error: '無法獲取商品數據',
      message: error.message 
    });
  }
});

// 獲取單個商品詳情
app.get('/api/products/:id', (req, res) => {
  try {
    const { id } = req.params;
    console.log(`收到獲取商品詳情請求: ${id}`);
    
    const mockDataPath = path.join(__dirname, 'src', 'data', 'mockProducts.json');
    if (fs.existsSync(mockDataPath)) {
      const mockData = JSON.parse(fs.readFileSync(mockDataPath, 'utf8'));
      const product = mockData.find(p => p.id === parseInt(id));
      
      if (!product) {
        return res.status(404).json({ error: '商品不存在' });
      }
      
      res.json(product);
    } else {
      res.status(404).json({ error: '商品不存在' });
    }
    
  } catch (error) {
    console.error('獲取商品詳情失敗:', error);
    res.status(500).json({ 
      error: '無法獲取商品詳情',
      message: error.message 
    });
  }
});

// 健康檢查端點
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: '服務器運行正常' });
});

// 啟動服務器
app.listen(PORT, () => {
  console.log(`✅ 服務器已啟動在 http://localhost:${PORT}`);
  console.log('可用的 API 端點:');
  console.log(`  - GET /api/products - 獲取所有商品`);
  console.log(`  - GET /api/products/:id - 獲取單個商品詳情`);
  console.log(`  - GET /api/health - 健康檢查`);
  console.log(`  - GET /images/* - 靜態圖片服務`);
});
