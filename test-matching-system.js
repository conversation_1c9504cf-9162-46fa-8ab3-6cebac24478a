// 測試商品比對系統
const testMatchingSystem = async () => {
  console.log('=== 測試商品比對系統 ===');
  
  try {
    console.log('\n1. 檢查當前商品庫存...');
    const productsResponse = await fetch('http://localhost:5000/api/products');
    const products = await productsResponse.json();
    
    console.log(`商品總數: ${products.length}`);
    products.forEach(product => {
      console.log(`- ${product.productName}`);
      console.log(`  SKU: ${product.sku}, 庫存: ${product.stockQuantity}`);
    });
    
    console.log('\n2. 檢查當前訂單...');
    const ordersResponse = await fetch('http://localhost:5000/api/orders');
    const orders = await ordersResponse.json();
    
    console.log(`訂單總數: ${orders.length}`);
    orders.forEach(order => {
      console.log(`- 訂單 ${order.orderNumber} (${order.customerName})`);
      console.log(`  訂購日期: ${new Date(order.orderDate).toLocaleDateString('zh-TW')}`);
      if (order.items) {
        order.items.forEach(item => {
          console.log(`  * ${item.name} (SKU: ${item.sku}, 數量: ${item.quantity})`);
        });
      }
    });
    
    console.log('\n3. 模擬商品比對邏輯...');
    
    // 模擬比對邏輯
    const allOrderItems = [];
    orders.forEach(order => {
      if (order.items && Array.isArray(order.items)) {
        order.items.forEach(item => {
          allOrderItems.push({
            ...item,
            orderId: order.id,
            orderNumber: order.orderNumber,
            customerName: order.customerName,
            orderDate: new Date(order.orderDate)
          });
        });
      }
    });
    
    // 按訂購日期排序
    allOrderItems.sort((a, b) => a.orderDate - b.orderDate);
    
    console.log('\n按優先順序排列的訂單項目:');
    allOrderItems.forEach((item, index) => {
      console.log(`${index + 1}. ${item.customerName} - ${item.name}`);
      console.log(`   SKU: ${item.sku}, 數量: ${item.quantity}`);
      console.log(`   訂購日期: ${item.orderDate.toLocaleDateString('zh-TW')}`);
    });
    
    console.log('\n4. 尋找匹配的商品...');
    
    const fuzzyMatchSKU = (orderSKU, productSKU) => {
      if (!orderSKU || !productSKU) return false;
      const cleanOrderSKU = orderSKU.replace(/-\d+$/, '').toLowerCase();
      const cleanProductSKU = productSKU.replace(/-\d+$/, '').toLowerCase();
      return cleanOrderSKU === cleanProductSKU;
    };
    
    const fuzzyMatchName = (orderName, productName) => {
      if (!orderName || !productName) return false;
      const cleanOrderName = orderName.toLowerCase().trim();
      const cleanProductName = productName.toLowerCase().trim();
      return cleanOrderName === cleanProductName || 
             cleanOrderName.includes(cleanProductName) || 
             cleanProductName.includes(cleanOrderName);
    };
    
    const matchResults = [];
    const productStock = {};
    
    // 初始化庫存追蹤
    products.forEach(product => {
      productStock[product.id] = product.stockQuantity;
    });
    
    for (const orderItem of allOrderItems) {
      const matchedProduct = products.find(product => {
        const nameMatch = fuzzyMatchName(orderItem.name, product.productName);
        const skuMatch = fuzzyMatchSKU(orderItem.sku, product.sku);
        return nameMatch && skuMatch;
      });
      
      const result = {
        orderItem,
        matchedProduct,
        allocated: false,
        reason: ''
      };
      
      if (matchedProduct) {
        const availableStock = productStock[matchedProduct.id] || 0;
        const requiredQuantity = orderItem.quantity || 1;
        
        if (availableStock >= requiredQuantity) {
          result.allocated = true;
          result.reason = `成功分派 ${requiredQuantity} 個商品`;
          productStock[matchedProduct.id] -= requiredQuantity;
          
          console.log(`✅ 匹配成功: ${orderItem.customerName}`);
          console.log(`   訂單商品: ${orderItem.name} (${orderItem.sku})`);
          console.log(`   匹配商品: ${matchedProduct.productName} (${matchedProduct.sku})`);
          console.log(`   分派數量: ${requiredQuantity}, 剩餘庫存: ${productStock[matchedProduct.id]}`);
        } else {
          result.reason = `庫存不足（需要 ${requiredQuantity}，剩餘 ${availableStock}）`;
          console.log(`❌ 庫存不足: ${orderItem.customerName} - ${orderItem.name}`);
        }
      } else {
        result.reason = '找不到匹配的商品';
        console.log(`❌ 無法匹配: ${orderItem.customerName} - ${orderItem.name} (${orderItem.sku})`);
      }
      
      matchResults.push(result);
    }
    
    console.log('\n5. 分派結果統計...');
    const successCount = matchResults.filter(r => r.allocated).length;
    const failCount = matchResults.filter(r => !r.allocated).length;
    const totalItems = matchResults.length;
    
    console.log(`總項目數: ${totalItems}`);
    console.log(`成功分派: ${successCount}`);
    console.log(`分派失敗: ${failCount}`);
    console.log(`成功率: ${totalItems > 0 ? (successCount / totalItems * 100).toFixed(1) : 0}%`);
    
    console.log('\n6. 更新後的商品庫存...');
    products.forEach(product => {
      const newStock = productStock[product.id];
      const changed = newStock !== product.stockQuantity;
      console.log(`- ${product.productName}: ${product.stockQuantity} -> ${newStock} ${changed ? '(已變更)' : ''}`);
    });
    
    console.log('\n✅ 商品比對系統測試完成');
    
  } catch (error) {
    console.error('❌ 測試失敗:', error);
  }
};

// 執行測試
testMatchingSystem();
