const axios = require('axios');
require('dotenv').config();

/**
 * 測試不同的 EasyStore API 端點格式
 */
async function testDifferentAPIFormats() {
  const apiKey = process.env.EASYSTORE_API_KEY;
  const baseUrls = [
    // 原始 URL
    process.env.EASYSTORE_API_URL,
    // 可能的其他格式
    'https://api.easystore.co/api/3.0/orders',
    'https://api.easystore.co/api/v3/orders',
    // 如果需要商店子域名的格式（您需要替換 YOUR_STORE 為實際商店名稱）
    // 'https://YOUR_STORE.api.easystore.co/api/3.0/orders'
  ];

  for (const url of baseUrls) {
    if (!url) continue;

    console.log(`\n正在測試 URL: ${url}`);

    try {
      const response = await axios.get(url, {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });

      console.log('✅ API 連接成功!');
      console.log('狀態碼:', response.status);
      console.log('響應數據結構:');
      console.log(JSON.stringify(response.data, null, 2));

      // 找到成功的 URL 後，測試訂單數據
      await testOrderData(response.data, url);
      return; // 成功後退出循環

    } catch (error) {
      console.log('❌ 連接失敗:', error.message);
      if (error.response) {
        console.log('   狀態碼:', error.response.status);
        console.log('   錯誤詳情:', error.response.data);
      }
    }
  }

  console.log('\n所有 URL 都測試失敗，請檢查：');
  console.log('1. API Key 是否正確');
  console.log('2. 是否需要商店子域名');
  console.log('3. API 端點格式是否正確');
}

/**
 * 測試訂單數據結構
 */
async function testOrderData(data, successUrl) {
  console.log(`\n成功的 API URL: ${successUrl}`);

  // 檢查數據結構
  if (data && data.orders && data.orders.length > 0) {
    console.log('\n=== 第一筆訂單詳細數據 ===');
    const firstOrder = data.orders[0];
    console.log(JSON.stringify(firstOrder, null, 2));

    console.log('\n=== 訂單關鍵字段摘要 ===');
    console.log('訂單ID:', firstOrder.id);
    console.log('訂單編號:', firstOrder.order_number || firstOrder.id);
    console.log('客戶信息:', firstOrder.customer);
    console.log('訂單狀態:', firstOrder.status);
    console.log('總金額:', firstOrder.total_price);
    console.log('創建時間:', firstOrder.created_at);
    console.log('商品項目數量:', firstOrder.line_items ? firstOrder.line_items.length : 0);

    if (firstOrder.line_items && firstOrder.line_items.length > 0) {
      console.log('\n=== 商品項目詳情 ===');
      firstOrder.line_items.forEach((item, index) => {
        console.log(`商品 ${index + 1}:`, {
          name: item.name,
          quantity: item.quantity,
          price: item.price,
          sku: item.sku
        });
      });
    }

    // 測試數據庫保存
    console.log('\n=== 準備測試數據庫保存 ===');
    await testDatabaseSave(firstOrder);

  } else if (data && Array.isArray(data)) {
    console.log('\n數據是數組格式，第一個項目:');
    console.log(JSON.stringify(data[0], null, 2));
  } else {
    console.log('\n數據結構:', typeof data);
    console.log(JSON.stringify(data, null, 2));
  }
}

/**
 * 測試數據庫保存功能
 */
async function testDatabaseSave(orderData) {
  try {
    const sequelize = require('./src/config/database');
    const Order = require('./src/models/Order');

    // 測試數據庫連接
    console.log('正在測試數據庫連接...');
    await sequelize.authenticate();
    console.log('✅ 數據庫連接成功');

    // 同步數據庫模型
    console.log('正在同步數據庫模型...');
    await sequelize.sync();
    console.log('✅ 數據庫模型同步成功');

    // 轉換訂單數據格式
    const orderToSave = {
      orderNumber: orderData.order_number || orderData.id.toString(),
      customerName: orderData.customer ?
        `${orderData.customer.first_name || ''} ${orderData.customer.last_name || ''}`.trim() || '未知客戶' : '未知客戶',
      customerEmail: orderData.customer?.email || null,
      customerPhone: orderData.customer?.phone || null,
      orderDate: new Date(orderData.created_at),
      status: orderData.status || 'pending',
      totalAmount: parseFloat(orderData.total_price) || 0,
      items: orderData.line_items || [],
      shippingAddress: orderData.shipping_address || {},
      billingAddress: orderData.billing_address || null,
      paymentMethod: orderData.payment_method || null,
      paymentStatus: orderData.payment_status || 'pending',
      notes: orderData.notes || null,
      easyStoreOrderId: orderData.id.toString()
    };

    console.log('\n準備保存的訂單數據:');
    console.log(JSON.stringify(orderToSave, null, 2));

    // 保存到數據庫
    const [savedOrder, created] = await Order.findOrCreate({
      where: { easyStoreOrderId: orderToSave.easyStoreOrderId },
      defaults: orderToSave
    });

    if (created) {
      console.log('\n✅ 訂單成功保存到數據庫');
    } else {
      console.log('\n⚠️ 訂單已存在，正在更新...');
      await savedOrder.update(orderToSave);
      console.log('✅ 訂單更新成功');
    }

    console.log('保存的訂單ID:', savedOrder.id);

  } catch (error) {
    console.error('\n❌ 數據庫操作失敗:', error.message);
    console.error('詳細錯誤:', error);
  }
}

// 執行測試
testDifferentAPIFormats();
