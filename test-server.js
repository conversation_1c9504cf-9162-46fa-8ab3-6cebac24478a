const express = require('express');
const cors = require('cors');
const sequelize = require('./src/config/database');
const ProductInfo = require('./src/models/ProductInfo');
require('dotenv').config();

const app = express();
const PORT = 5000;

// 中間件
app.use(cors());
app.use(express.json());

// 測試路由
app.get('/api/health', (req, res) => {
  console.log('收到健康檢查請求');
  res.json({ 
    status: 'OK', 
    message: 'API 服務器運行正常',
    timestamp: new Date().toISOString()
  });
});

// 模擬訂單數據
app.get('/api/orders', (req, res) => {
  console.log('收到獲取訂單請求');
  
  const mockOrders = [
    {
      id: 1,
      orderNumber: "ES-18506",
      customerName: "測試客戶",
      customerEmail: "<EMAIL>",
      customerPhone: "+886-912-345-678",
      orderDate: new Date().toISOString(),
      status: "pending",
      totalAmount: 1250,
      items: [
        {
          id: 1,
          name: "Pink Floyd - The Dark Side of the Moon (Vinyl)",
          sku: "VINYL-PF-DSOTM",
          quantity: 1,
          price: 850,
          variant: "Black Vinyl"
        }
      ]
    }
  ];
  
  res.json(mockOrders);
});

// 訂單統計
app.get('/api/orders/stats', (req, res) => {
  console.log('收到獲取訂單統計請求');
  
  const stats = {
    total: 1,
    pending: 1,
    completed: 0,
    processing: 0
  };
  
  res.json(stats);
});

// 同步訂單
app.post('/api/orders/sync', (req, res) => {
  console.log('收到同步訂單請求');
  res.json({ 
    success: true, 
    message: '訂單同步功能開發中，目前顯示模擬數據' 
  });
});

// 更新訂單狀態
app.put('/api/orders/:id/status', (req, res) => {
  const { id } = req.params;
  const { status } = req.body;

  console.log(`更新訂單 ${id} 狀態為: ${status}`);

  res.json({
    success: true,
    message: '訂單狀態更新成功（模擬）'
  });
});

// 初始化數據庫連接
sequelize.authenticate()
  .then(() => {
    console.log('✅ 數據庫連接成功');
    return sequelize.sync();
  })
  .then(() => {
    console.log('✅ 數據庫模型同步成功');
  })
  .catch(err => {
    console.error('❌ 數據庫連接失敗:', err);
  });

// 獲取所有商品（從 PostgreSQL 數據庫）
app.get('/api/products', async (req, res) => {
  console.log('收到獲取所有商品請求');

  try {
    // 確保數據庫連接
    await sequelize.authenticate();

    // 從數據庫獲取所有商品
    const products = await ProductInfo.findAll({
      order: [['createdAt', 'DESC']]
    });

    // 轉換為前端需要的格式
    const formattedProducts = products.map(product => ({
      id: product.id,
      productId: product.productId,
      productName: product.productName,
      sku: product.sku,
      upc: product.upc,
      artist: product.artist,
      brand: product.brand,
      supplier: product.supplier,
      category: product.category,
      price: parseFloat(product.price) || 0,
      stockQuantity: product.stockQuantity || 0,
      isPublished: product.isPublished,
      productImages: product.productImages || [],
      tags: product.tags || [],
      status: product.status || 'active',
      specifications: product.specifications || {},
      createdAt: product.createdAt,
      updatedAt: product.updatedAt
    }));

    console.log(`從數據庫獲取到 ${formattedProducts.length} 個商品`);
    res.json(formattedProducts);

  } catch (error) {
    console.error('獲取商品失敗:', error);
    res.status(500).json({
      error: '獲取商品失敗',
      message: error.message
    });
  }
});

// 商品匯入API - 保存到 PostgreSQL 數據庫
app.post('/api/products/import', async (req, res) => {
  const { products, type } = req.body;

  console.log(`收到商品匯入請求 - 類型: ${type}, 商品數量: ${products.length}`);

  try {
    // 確保數據庫連接
    await sequelize.authenticate();
    await sequelize.sync();

    let importedCount = 0;
    let updatedCount = 0;
    const results = [];

    for (const product of products) {
      try {
        // 檢查是否已存在相同 UPC 的商品
        const existingProduct = await ProductInfo.findOne({
          where: { upc: product.upc }
        });

        const productData = {
          productId: product.upc || `IMPORT-${Date.now()}`,
          productName: product.productName,
          sku: product.sku || `SKU-${Date.now()}`,
          upc: product.upc,
          artist: product.artist || '未知藝術家',
          brand: product.brand || '未知品牌',
          supplier: product.supplier || '未知供應商',
          category: product.category || null,
          price: parseFloat(product.price) || 0,
          costPrice: parseFloat(product.costPrice) || 0,
          stockQuantity: product.actualQuantity || product.expectedQuantity || 0,
          reservedQuantity: 0,
          availableQuantity: product.actualQuantity || product.expectedQuantity || 0,
          isPublished: type === 'verified' ? true : false,
          publishedAt: type === 'verified' ? new Date() : null,
          productImages: product.productImages || [],
          productVariants: product.productVariants || [],
          tags: product.tags || [],
          description: product.description || '',
          specifications: product.specifications || {},
          weight: parseFloat(product.weight) || 0,
          dimensions: product.dimensions || {},
          status: 'active',
          lastSyncAt: new Date()
        };

        if (existingProduct) {
          // 更新現有商品 - 合併庫存數量
          const newStockQuantity = existingProduct.stockQuantity + productData.stockQuantity;
          const newAvailableQuantity = existingProduct.availableQuantity + productData.availableQuantity;

          await existingProduct.update({
            ...productData,
            stockQuantity: newStockQuantity,
            availableQuantity: newAvailableQuantity,
            // 如果是已檢驗的商品，更新上架狀態
            isPublished: type === 'verified' ? true : existingProduct.isPublished,
            publishedAt: type === 'verified' && !existingProduct.publishedAt ? new Date() : existingProduct.publishedAt
          });

          console.log(`✅ 更新現有商品: ${product.productName} (UPC: ${product.upc})`);
          console.log(`   庫存數量: ${existingProduct.stockQuantity} + ${productData.stockQuantity} = ${newStockQuantity}`);

          results.push({
            action: 'updated',
            product: existingProduct,
            oldStock: existingProduct.stockQuantity - productData.stockQuantity,
            newStock: newStockQuantity
          });

          updatedCount++;
        } else {
          // 創建新商品
          const newProduct = await ProductInfo.create(productData);

          console.log(`✅ 創建新商品: ${product.productName} (UPC: ${product.upc})`);

          results.push({
            action: 'created',
            product: newProduct
          });

          importedCount++;
        }

      } catch (productError) {
        console.error(`❌ 處理商品失敗: ${product.productName}`, productError.message);
        results.push({
          action: 'failed',
          product: product,
          error: productError.message
        });
      }
    }

    console.log(`商品匯入完成 - 新增: ${importedCount}, 更新: ${updatedCount}`);

    res.json({
      success: true,
      message: `商品匯入完成 - 新增 ${importedCount} 個，更新 ${updatedCount} 個商品`,
      importedCount,
      updatedCount,
      totalProcessed: importedCount + updatedCount,
      importType: type,
      results
    });

  } catch (error) {
    console.error('商品匯入失敗:', error);
    res.status(500).json({
      success: false,
      message: '商品匯入失敗',
      error: error.message
    });
  }
});

// 存儲已分派的項目（在實際應用中應該使用數據庫）
let allocatedItemsStore = new Set();

// 商品分派API
app.post('/api/products/allocate', async (req, res) => {
  const { allocations } = req.body;

  console.log(`收到商品分派請求，分派項目數量: ${allocations.length}`);

  try {
    // 確保數據庫連接
    await sequelize.authenticate();

    const results = [];

    for (const allocation of allocations) {
      try {
        const { productId, quantity, orderId, orderItemId } = allocation;

        // 創建唯一的分派標識
        const allocationKey = `${orderId}-${orderItemId || 0}-${productId}`;

        // 檢查是否已經分派過
        if (allocatedItemsStore.has(allocationKey)) {
          console.log(`⚠️ 跳過重複分派: ${allocationKey}`);
          results.push({
            success: false,
            productId,
            orderId,
            message: '該項目已經分派過，跳過重複分派',
            skipped: true
          });
          continue;
        }

        // 查找商品
        const product = await ProductInfo.findByPk(productId);
        if (!product) {
          results.push({
            success: false,
            productId,
            orderId,
            message: '商品不存在'
          });
          continue;
        }

        // 檢查庫存
        if (product.stockQuantity < quantity) {
          results.push({
            success: false,
            productId,
            orderId,
            message: `庫存不足，需要 ${quantity}，剩餘 ${product.stockQuantity}`
          });
          continue;
        }

        // 扣減庫存
        const newStockQuantity = product.stockQuantity - quantity;
        const newAvailableQuantity = Math.max(0, product.availableQuantity - quantity);

        await product.update({
          stockQuantity: newStockQuantity,
          availableQuantity: newAvailableQuantity,
          lastSyncAt: new Date()
        });

        // 記錄已分派項目
        allocatedItemsStore.add(allocationKey);

        console.log(`✅ 成功分派商品: ${product.productName}`);
        console.log(`   分派標識: ${allocationKey}`);
        console.log(`   庫存: ${product.stockQuantity + quantity} -> ${newStockQuantity}`);

        results.push({
          success: true,
          productId,
          orderId,
          quantity,
          oldStock: product.stockQuantity + quantity,
          newStock: newStockQuantity,
          message: '分派成功',
          allocationKey
        });

      } catch (allocationError) {
        console.error('分派項目失敗:', allocationError);
        results.push({
          success: false,
          productId: allocation.productId,
          orderId: allocation.orderId,
          message: allocationError.message
        });
      }
    }

    const successCount = results.filter(r => r.success).length;
    const skippedCount = results.filter(r => r.skipped).length;
    const failCount = results.filter(r => !r.success && !r.skipped).length;

    console.log(`商品分派完成 - 成功: ${successCount}, 跳過: ${skippedCount}, 失敗: ${failCount}`);
    console.log(`已分派項目總數: ${allocatedItemsStore.size}`);

    res.json({
      success: true,
      message: `商品分派完成 - 成功 ${successCount} 項，跳過 ${skippedCount} 項，失敗 ${failCount} 項`,
      successCount,
      skippedCount,
      failCount,
      results
    });

  } catch (error) {
    console.error('商品分派失敗:', error);
    res.status(500).json({
      success: false,
      message: '商品分派失敗',
      error: error.message
    });
  }
});

// 重置分派狀態API
app.post('/api/products/reset-allocations', (req, res) => {
  console.log('重置分派狀態');
  allocatedItemsStore.clear();

  res.json({
    success: true,
    message: '分派狀態已重置'
  });
});

// 錯誤處理
app.use((err, req, res, next) => {
  console.error('服務器錯誤:', err);
  res.status(500).json({
    error: '服務器內部錯誤',
    message: err.message
  });
});

// 404 處理
app.use((req, res) => {
  console.log(`404 - 找不到路徑: ${req.originalUrl}`);
  res.status(404).json({
    error: '端點不存在',
    path: req.originalUrl
  });
});

// 啟動服務器
app.listen(PORT, () => {
  console.log(`🚀 測試 API 服務器運行在 http://localhost:${PORT}`);
  console.log(`📊 健康檢查: http://localhost:${PORT}/api/health`);
  console.log(`📋 訂單 API: http://localhost:${PORT}/api/orders`);
});
