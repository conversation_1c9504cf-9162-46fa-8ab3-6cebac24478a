import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Tag, 
  Space, 
  Button, 
  message, 
  Spin, 
  Card, 
  Statistic, 
  Row, 
  Col,
  Modal,
  Descriptions,
  Select,
  Tooltip
} from 'antd';
import {
  ReloadOutlined,
  SyncOutlined,
  EyeOutlined,
  ShoppingCartOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';

const { Option } = Select;

function OrdersPage() {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({});
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);

  // 獲取訂單數據
  const fetchOrders = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:5000/api/orders');
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      setOrders(data);
      message.success(`成功載入 ${data.length} 筆訂單`);
      
    } catch (error) {
      console.error('獲取訂單失敗:', error);
      message.error('無法獲取訂單數據，請檢查後端服務器是否運行');
    } finally {
      setLoading(false);
    }
  };

  // 獲取訂單統計
  const fetchStats = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/orders/stats');
      
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('獲取統計失敗:', error);
    }
  };

  // 同步訂單
  const syncOrders = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:5000/api/orders/sync', {
        method: 'POST'
      });
      
      if (response.ok) {
        const data = await response.json();
        message.success(data.message);
        fetchOrders();
        fetchStats();
      } else {
        throw new Error('同步失敗');
      }
    } catch (error) {
      console.error('同步訂單失敗:', error);
      message.error('訂單同步失敗');
    } finally {
      setLoading(false);
    }
  };

  // 更新訂單狀態
  const updateOrderStatus = async (orderId, newStatus) => {
    try {
      const response = await fetch(`http://localhost:5000/api/orders/${orderId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: newStatus })
      });
      
      if (response.ok) {
        message.success('訂單狀態更新成功');
        fetchOrders();
        fetchStats();
      } else {
        throw new Error('更新失敗');
      }
    } catch (error) {
      console.error('更新訂單狀態失敗:', error);
      message.error('更新訂單狀態失敗');
    }
  };

  // 查看訂單詳情
  const viewOrderDetail = (order) => {
    setSelectedOrder(order);
    setModalVisible(true);
  };

  useEffect(() => {
    fetchOrders();
    fetchStats();
  }, []);

  // 狀態標籤顏色映射
  const getStatusColor = (status) => {
    const colorMap = {
      'pending': 'gold',
      'processing': 'blue',
      'shipped': 'cyan',
      'completed': 'green',
      'cancelled': 'red',
      'refunded': 'purple'
    };
    return colorMap[status] || 'default';
  };

  // 狀態中文映射
  const getStatusText = (status) => {
    const textMap = {
      'pending': '待處理',
      'processing': '處理中',
      'shipped': '已出貨',
      'completed': '已完成',
      'cancelled': '已取消',
      'refunded': '已退款'
    };
    return textMap[status] || status;
  };

  // 表格列定義
  const columns = [
    {
      title: '訂單編號',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      render: (text, record) => (
        <Button 
          type="link" 
          onClick={() => viewOrderDetail(record)}
          style={{ padding: 0 }}
        >
          {text}
        </Button>
      ),
    },
    {
      title: '客戶名稱',
      dataIndex: 'customerName',
      key: 'customerName',
    },
    {
      title: '客戶電子郵件',
      dataIndex: 'customerEmail',
      key: 'customerEmail',
      render: (email) => email || '-',
    },
    {
      title: '訂單日期',
      dataIndex: 'orderDate',
      key: 'orderDate',
      render: (date) => new Date(date).toLocaleDateString('zh-TW'),
    },
    {
      title: '狀態',
      dataIndex: 'status',
      key: 'status',
      render: (status, record) => (
        <Select
          value={status}
          style={{ width: 100 }}
          size="small"
          onChange={(newStatus) => updateOrderStatus(record.id, newStatus)}
        >
          <Option value="pending">待處理</Option>
          <Option value="processing">處理中</Option>
          <Option value="shipped">已出貨</Option>
          <Option value="completed">已完成</Option>
          <Option value="cancelled">已取消</Option>
          <Option value="refunded">已退款</Option>
        </Select>
      ),
    },
    {
      title: '總金額',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      render: (amount) => `NT$${parseFloat(amount).toLocaleString()}`,
      sorter: (a, b) => parseFloat(a.totalAmount) - parseFloat(b.totalAmount),
    },
    {
      title: '商品數量',
      dataIndex: 'items',
      key: 'itemCount',
      render: (items) => items ? items.length : 0,
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="查看詳情">
            <Button 
              type="text" 
              icon={<EyeOutlined />}
              onClick={() => viewOrderDetail(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div>
      {/* 頁面標題和操作按鈕 */}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <h1>所有訂單</h1>
        <Space>
          <Button 
            icon={<ReloadOutlined />}
            onClick={fetchOrders}
            loading={loading}
          >
            重新載入
          </Button>
          <Button 
            type="primary" 
            icon={<SyncOutlined />}
            onClick={syncOrders}
            loading={loading}
          >
            同步 EasyStore 訂單
          </Button>
        </Space>
      </div>

      {/* 統計卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="總訂單數"
              value={stats.total || 0}
              prefix={<ShoppingCartOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="待處理"
              value={stats.pending || 0}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="處理中"
              value={stats.processing || 0}
              prefix={<SyncOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已完成"
              value={stats.completed || 0}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>
      
      {/* 訂單表格 */}
      <Card>
        <Spin spinning={loading}>
          <Table 
            columns={columns} 
            dataSource={orders} 
            rowKey="id"
            pagination={{ 
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 項，共 ${total} 項`
            }}
            scroll={{ x: 1200 }}
          />
        </Spin>
      </Card>

      {/* 訂單詳情模態框 */}
      <Modal
        title={`訂單詳情 - ${selectedOrder?.orderNumber}`}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedOrder && (
          <div>
            <Descriptions bordered column={2}>
              <Descriptions.Item label="訂單編號">{selectedOrder.orderNumber}</Descriptions.Item>
              <Descriptions.Item label="EasyStore ID">{selectedOrder.easyStoreOrderId}</Descriptions.Item>
              <Descriptions.Item label="客戶名稱">{selectedOrder.customerName}</Descriptions.Item>
              <Descriptions.Item label="客戶電子郵件">{selectedOrder.customerEmail || '-'}</Descriptions.Item>
              <Descriptions.Item label="客戶電話">{selectedOrder.customerPhone || '-'}</Descriptions.Item>
              <Descriptions.Item label="訂單狀態">
                <Tag color={getStatusColor(selectedOrder.status)}>
                  {getStatusText(selectedOrder.status)}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="訂單日期">
                {new Date(selectedOrder.orderDate).toLocaleString('zh-TW')}
              </Descriptions.Item>
              <Descriptions.Item label="總金額">
                NT${parseFloat(selectedOrder.totalAmount).toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label="付款方式">{selectedOrder.paymentMethod || '-'}</Descriptions.Item>
              <Descriptions.Item label="付款狀態">{selectedOrder.paymentStatus || '-'}</Descriptions.Item>
              <Descriptions.Item label="備註" span={2}>{selectedOrder.notes || '-'}</Descriptions.Item>
            </Descriptions>

            {/* 商品列表 */}
            {selectedOrder.items && selectedOrder.items.length > 0 && (
              <div style={{ marginTop: 16 }}>
                <h4>訂單商品</h4>
                <Table
                  size="small"
                  dataSource={selectedOrder.items}
                  rowKey={(item, index) => index}
                  pagination={false}
                  columns={[
                    { title: '商品名稱', dataIndex: 'name', key: 'name' },
                    { title: 'SKU', dataIndex: 'sku', key: 'sku' },
                    { title: '數量', dataIndex: 'quantity', key: 'quantity' },
                    { title: '單價', dataIndex: 'price', key: 'price', render: (price) => `NT$${price}` },
                    { title: '變體', dataIndex: 'variant', key: 'variant' }
                  ]}
                />
              </div>
            )}

            {/* 配送地址 */}
            {selectedOrder.shippingAddress && (
              <div style={{ marginTop: 16 }}>
                <h4>配送地址</h4>
                <p>
                  {selectedOrder.shippingAddress.name}<br/>
                  {selectedOrder.shippingAddress.address1}<br/>
                  {selectedOrder.shippingAddress.address2 && `${selectedOrder.shippingAddress.address2}<br/>`}
                  {selectedOrder.shippingAddress.city}, {selectedOrder.shippingAddress.province} {selectedOrder.shippingAddress.zip}<br/>
                  {selectedOrder.shippingAddress.country}
                </p>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
}

export default OrdersPage;
