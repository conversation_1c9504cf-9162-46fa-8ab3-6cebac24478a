// 測試掃描商品重新排序功能
console.log('=== 測試掃描商品重新排序功能 ===');

// 模擬商品列表
const mockProducts = [
  { id: 1, productName: 'Product A', upc: 'UPC001', actualQuantity: 0 },
  { id: 2, productName: 'Product B', upc: 'UPC002', actualQuantity: 0 },
  { id: 3, productName: 'Product C', upc: 'UPC003', actualQuantity: 0 }
];

console.log('\n初始商品順序:');
mockProducts.forEach((product, index) => {
  console.log(`${index + 1}. ${product.productName} (${product.upc})`);
});

// 模擬掃描 Product C
console.log('\n掃描 Product C (UPC003)...');

const scannedUPC = 'UPC003';
const scannedProduct = mockProducts.find(p => p.upc === scannedUPC);

if (scannedProduct) {
  // 將掃描的商品移到第一位
  const otherProducts = mockProducts.filter(p => p.id !== scannedProduct.id);
  const reorderedProducts = [scannedProduct, ...otherProducts];
  
  console.log('\n掃描後的商品順序:');
  reorderedProducts.forEach((product, index) => {
    const isScanned = product.id === scannedProduct.id;
    console.log(`${index + 1}. ${product.productName} (${product.upc}) ${isScanned ? '← 剛掃描' : ''}`);
  });
  
  console.log(`\n前個被掃描商品: ${mockProducts[0].productName} (${mockProducts[0].upc})`);
  
  // 再次掃描 Product A
  console.log('\n再次掃描 Product A (UPC001)...');
  const secondScannedUPC = 'UPC001';
  const secondScannedProduct = reorderedProducts.find(p => p.upc === secondScannedUPC);
  
  if (secondScannedProduct) {
    const previousFirst = reorderedProducts[0];
    const otherProducts2 = reorderedProducts.filter(p => p.id !== secondScannedProduct.id);
    const finalProducts = [secondScannedProduct, ...otherProducts2];
    
    console.log('\n第二次掃描後的商品順序:');
    finalProducts.forEach((product, index) => {
      const isScanned = product.id === secondScannedProduct.id;
      console.log(`${index + 1}. ${product.productName} (${product.upc}) ${isScanned ? '← 剛掃描' : ''}`);
    });
    
    console.log(`\n前個被掃描商品: ${previousFirst.productName} (${previousFirst.upc})`);
  }
}

console.log('\n✅ 掃描重新排序功能測試完成');

// 測試前端功能的說明
console.log('\n📋 前端測試步驟:');
console.log('1. 打開到貨商品檢驗頁面');
console.log('2. 在右側"掃碼輸入"框中輸入商品UPC');
console.log('3. 按Enter確認掃描');
console.log('4. 觀察左側商品列表是否將掃描的商品移到第一位');
console.log('5. 觀察右側是否顯示"前個被掃描商品"區域');
console.log('6. 再次掃描不同商品，確認順序變化和前個商品顯示');

console.log('\n🎯 預期結果:');
console.log('- 掃描商品後，該商品會移到列表第一位');
console.log('- "前個被掃描商品"區域會顯示之前第一位的商品');
console.log('- 顯示商品圖片、名稱、UPC等資訊');
console.log('- 凍結窗格功能已完全移除');
