// 簡化的分派測試
async function testSimpleAllocation() {
  console.log('=== 簡化分派測試 ===');
  
  try {
    // 第一次分派
    console.log('第一次分派...');
    const firstResponse = await fetch('http://localhost:5000/api/products/allocate', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        allocations: [{ productId: 1, quantity: 1, orderId: 1, orderItemId: 1 }] 
      })
    });
    
    const firstResult = await firstResponse.json();
    console.log('第一次結果:', JSON.stringify(firstResult, null, 2));
    
    // 第二次分派（相同項目）
    console.log('\n第二次分派（相同項目）...');
    const secondResponse = await fetch('http://localhost:5000/api/products/allocate', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        allocations: [{ productId: 1, quantity: 1, orderId: 1, orderItemId: 1 }] 
      })
    });
    
    const secondResult = await secondResponse.json();
    console.log('第二次結果:', JSON.stringify(secondResult, null, 2));
    
    // 驗證
    if (firstResult.successCount === 1 && secondResult.skippedCount === 1) {
      console.log('\n✅ 防重複分派功能正常工作！');
    } else {
      console.log('\n❌ 防重複分派功能有問題');
    }
    
  } catch (error) {
    console.error('測試失敗:', error);
  }
}

testSimpleAllocation();
