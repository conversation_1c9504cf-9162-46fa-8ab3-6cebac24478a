const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const ProductInfo = sequelize.define('ProductInfo', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  productId: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    comment: 'EasyStore 商品ID'
  },
  productName: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: '商品名稱'
  },
  sku: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '商品SKU'
  },
  upc: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '商品UPC條碼'
  },
  artist: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '藝術家/歌手'
  },
  brand: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '商品品牌'
  },
  supplier: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '供應商'
  },
  category: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '商品分類'
  },
  price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: '商品價格'
  },
  costPrice: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: '成本價格'
  },
  stockQuantity: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '庫存數量'
  },
  reservedQuantity: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '預留數量'
  },
  availableQuantity: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '可用數量'
  },
  isPublished: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否已上架'
  },
  publishedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '上架時間'
  },
  productImages: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: '商品圖片URLs'
  },
  productVariants: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: '商品變體資訊'
  },
  tags: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: '商品標籤'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '商品描述'
  },
  specifications: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: '商品規格'
  },
  weight: {
    type: DataTypes.DECIMAL(8, 2),
    allowNull: true,
    comment: '商品重量(kg)'
  },
  dimensions: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: '商品尺寸 {length, width, height}'
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive', 'discontinued'),
    allowNull: false,
    defaultValue: 'active',
    comment: '商品狀態'
  },
  easyStoreData: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: '完整的EasyStore API響應數據'
  },
  lastSyncAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '最後同步時間'
  }
}, {
  tableName: 'product_info',
  timestamps: true,
  indexes: [
    {
      fields: ['productId']
    },
    {
      fields: ['sku']
    },
    {
      fields: ['upc']
    },
    {
      fields: ['brand']
    },
    {
      fields: ['supplier']
    },
    {
      fields: ['category']
    },
    {
      fields: ['isPublished']
    },
    {
      fields: ['status']
    },
    {
      fields: ['tags'], 
      using: 'gin'
    }
  ]
});

module.exports = ProductInfo;
