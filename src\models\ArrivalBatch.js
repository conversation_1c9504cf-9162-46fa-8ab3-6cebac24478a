const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const ArrivalBatch = sequelize.define('ArrivalBatch', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  batchId: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    comment: '到貨批次ID'
  },
  supplierName: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: '供應商名稱'
  },
  arrivalDate: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: '到貨日期'
  },
  status: {
    type: DataTypes.ENUM('pending', 'in_progress', 'completed', 'discrepancy'),
    allowNull: false,
    defaultValue: 'pending',
    comment: '批次狀態：待處理、處理中、已完成、有差異'
  },
  totalItems: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '總商品數量'
  },
  scannedItems: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '已掃描商品數量'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '備註'
  },
  importedBy: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '匯入人員'
  },
  checkedBy: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '檢驗人員'
  },
  excelFilePath: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '原始Excel檔案路徑'
  }
}, {
  tableName: 'arrival_batches',
  timestamps: true,
  indexes: [
    {
      fields: ['batchId']
    },
    {
      fields: ['supplierName']
    },
    {
      fields: ['status']
    }
  ]
});

// 關聯到 ProductArrival 模型
ArrivalBatch.associate = (models) => {
  ArrivalBatch.hasMany(models.ProductArrival, {
    foreignKey: 'arrivalBatchId',
    sourceKey: 'batchId',
    as: 'products'
  });
};

module.exports = ArrivalBatch;
