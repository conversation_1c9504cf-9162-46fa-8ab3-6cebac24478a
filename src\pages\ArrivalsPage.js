import { useState, useEffect, useRef } from 'react';
import {
  Table,
  Card,
  Row,
  Col,
  Button,
  message,
  Spin,
  Progress,
  Tag,
  Input,
  Upload,
  Modal,
  Select,
  Statistic,
  Badge,
  Image,
  Typography,
  Space,
  Divider,
  InputNumber,
  Tooltip
} from 'antd';
import {
  UploadOutlined,
  ScanOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  EyeOutlined,
  InboxOutlined,
  MinusOutlined,
  PlusOutlined,
  DatabaseOutlined,
  ImportOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { Option } = Select;
const { Dragger } = Upload;

function ArrivalsPage() {
  const [loading, setLoading] = useState(false);
  const [arrivalBatches, setArrivalBatches] = useState([]);
  const [selectedBatch, setSelectedBatch] = useState(null);
  const [arrivalProducts, setArrivalProducts] = useState([]);
  const [scannedProduct, setScannedProduct] = useState(null);
  const [scanInput, setScanInput] = useState('');
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [selectedSupplier, setSelectedSupplier] = useState('');
  const [previousScannedProduct, setPreviousScannedProduct] = useState(null);
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [importType, setImportType] = useState('verified'); // 'verified' 或 'all'

  const scanInputRef = useRef(null);

  // 模擬供應商列表
  const suppliers = [
    'Universal Music',
    'Sony Music',
    'Warner Music',
    'Independent Records',
    'Vinyl Distributor'
  ];

  // 模擬到貨商品數據
  const mockArrivalProducts = [
    {
      id: 1,
      productName: 'Pink Floyd - The Dark Side of the Moon',
      upc: '602537691593',
      artist: 'Pink Floyd',
      expectedQuantity: 5,
      actualQuantity: 3,
      specifications: { color: 'Black Vinyl', edition: 'Remastered' },
      productImage: ["/images/product_3_Pink Floyd - The Dark Side of the Moon.webp"],
      status: 'checking'
    },
    {
      id: 2,
      productName: 'Led Zeppelin - IV',
      upc: '081227971816',
      artist: 'Led Zeppelin',
      expectedQuantity: 3,
      actualQuantity: 3,
      specifications: { color: 'Black Vinyl', edition: 'Original' },
      productImage:["/images/product_2_Led Zeppelin - IV.webp"],
      status: 'completed'
    },
    {
      id: 3,
      productName: 'The Beatles - Abbey Road',
      upc: '094638241614',
      artist: 'The Beatles',
      expectedQuantity: 4,
      actualQuantity: 0,
      specifications: { color: 'Black Vinyl', edition: '50th Anniversary' },
      productImage: ["/images/product_5_The Beatles - Abbey Road.webp"],
      status: 'pending'
    },
    {
      id: 4,
      productName: 'Queen - Bohemian Rhapsody',
      upc: '602547202914',
      artist: 'Queen',
      expectedQuantity: 2,
      actualQuantity: 1,
      specifications: { color: 'Colored Vinyl', edition: 'Limited' },
      productImage: ["/images/product_4_Queen - Bohemian Rhapsody.webp"],
      status: 'checking'
    },
    {
      id: 5,
      productName: 'David Bowie - The Rise and Fall of Ziggy Stardust',
      upc: '190295851927',
      artist: 'David Bowie',
      expectedQuantity: 3,
      actualQuantity: 4,
      specifications: { color: 'Picture Disc', edition: 'Special' },
      productImage: ["/images/product_1_David Bowie - The Rise and Fall of Ziggy Stardust.webp"],
      status: 'discrepancy'
    }
  ];

  useEffect(() => {
    setArrivalProducts(mockArrivalProducts);
    // 聚焦到掃碼輸入框
    if (scanInputRef.current) {
      scanInputRef.current.focus();
    }
  }, []);

  // 處理掃碼輸入
  const handleScanInput = (e) => {
    if (e.key === 'Enter') {
      const upc = scanInput.trim();
      if (upc) {
        handleScan(upc);
        setScanInput('');
      }
    }
  };

  // 處理掃碼
  const handleScan = (upc) => {
    const product = arrivalProducts.find(p => p.upc === upc);

    if (product) {
      // 保存前一個被掃描的商品（當前列表第一個商品）
      if (arrivalProducts.length > 0 && arrivalProducts[0].id !== product.id) {
        setPreviousScannedProduct(arrivalProducts[0]);
      }

      // 更新實際數量
      const updatedProducts = arrivalProducts.map(p => {
        if (p.upc === upc) {
          const newActualQuantity = p.actualQuantity + 1;
          let newStatus = 'checking';

          if (newActualQuantity === p.expectedQuantity) {
            newStatus = 'completed';
          } else if (newActualQuantity > p.expectedQuantity) {
            newStatus = 'discrepancy';
          }

          return {
            ...p,
            actualQuantity: newActualQuantity,
            status: newStatus,
            lastScannedAt: new Date()
          };
        }
        return p;
      });

      // 將掃描的商品移到列表第一位
      const scannedProductUpdated = updatedProducts.find(p => p.id === product.id);
      const otherProducts = updatedProducts.filter(p => p.id !== product.id);
      const reorderedProducts = [scannedProductUpdated, ...otherProducts];

      setArrivalProducts(reorderedProducts);
      setScannedProduct({
        ...scannedProductUpdated,
        lastScannedAt: new Date()
      });

      message.success(`掃描成功: ${product.productName}`);
    } else {
      message.error(`找不到 UPC: ${upc} 的商品`);
      setScannedProduct({
        upc,
        productName: '未知商品',
        artist: '未知',
        productImage: 'https://via.placeholder.com/300x300/FF0000/FFFFFF?text=Unknown+Product',
        error: true
      });
    }
  };

  // 手動調整實際數量
  const handleQuantityChange = (productId, newQuantity) => {
    if (newQuantity < 0) {
      message.warning('數量不能小於 0');
      return;
    }

    const updatedProducts = arrivalProducts.map(p => {
      if (p.id === productId) {
        let newStatus = 'checking';

        if (newQuantity === 0) {
          newStatus = 'pending';
        } else if (newQuantity === p.expectedQuantity) {
          newStatus = 'completed';
        } else if (newQuantity > p.expectedQuantity) {
          newStatus = 'discrepancy';
        }

        return {
          ...p,
          actualQuantity: newQuantity,
          status: newStatus,
          lastScannedAt: newQuantity > 0 ? new Date() : null
        };
      }
      return p;
    });

    setArrivalProducts(updatedProducts);

    // 如果當前顯示的掃描商品是被修改的商品，也要更新
    if (scannedProduct && scannedProduct.id === productId) {
      const updatedProduct = updatedProducts.find(p => p.id === productId);
      setScannedProduct(updatedProduct);
    }

    message.success('數量已更新');
  };

  // 快速調整數量（+1 或 -1）
  const adjustQuantity = (productId, delta) => {
    const product = arrivalProducts.find(p => p.id === productId);
    if (product) {
      const newQuantity = Math.max(0, product.actualQuantity + delta);
      handleQuantityChange(productId, newQuantity);
    }
  };

  // 獲取狀態顏色
  const getStatusColor = (status) => {
    const colorMap = {
      'pending': 'default',
      'checking': 'processing',
      'completed': 'success',
      'discrepancy': 'error'
    };
    return colorMap[status] || 'default';
  };

  // 獲取狀態文字
  const getStatusText = (status) => {
    const textMap = {
      'pending': '待檢驗',
      'checking': '檢驗中',
      'completed': '已完成',
      'discrepancy': '有差異'
    };
    return textMap[status] || status;
  };

  // 計算統計數據
  const getStats = () => {
    const total = arrivalProducts.length;
    const completed = arrivalProducts.filter(p => p.status === 'completed').length;
    const checking = arrivalProducts.filter(p => p.status === 'checking').length;
    const discrepancy = arrivalProducts.filter(p => p.status === 'discrepancy').length;
    const pending = arrivalProducts.filter(p => p.status === 'pending').length;

    const totalExpected = arrivalProducts.reduce((sum, p) => sum + p.expectedQuantity, 0);
    const totalScanned = arrivalProducts.reduce((sum, p) => sum + p.actualQuantity, 0);

    return {
      total,
      completed,
      checking,
      discrepancy,
      pending,
      totalExpected,
      totalScanned,
      progress: totalExpected > 0 ? (totalScanned / totalExpected) * 100 : 0
    };
  };

  const stats = getStats();



  // 匯入已檢驗完成的商品
  const importVerifiedProducts = async () => {
    try {
      const verifiedProducts = arrivalProducts.filter(product =>
        product.status === 'verified' && product.expectedQuantity === product.actualQuantity
      );

      if (verifiedProducts.length === 0) {
        message.warning('沒有已檢驗完成的商品可以匯入');
        return;
      }

      // 調用後端API匯入商品
      const response = await fetch('http://localhost:5001/api/products/import', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ products: verifiedProducts, type: 'verified' })
      });

      const result = await response.json();

      if (result.success) {
        message.success(`成功匯入 ${result.importedCount} 個已檢驗完成的商品至商品資料庫`);

        // 從到貨商品列表中移除已匯入的商品
        const remainingProducts = arrivalProducts.filter(product =>
          !(product.status === 'verified' && product.expectedQuantity === product.actualQuantity)
        );
        setArrivalProducts(remainingProducts);
      } else {
        throw new Error(result.message || '匯入失敗');
      }

    } catch (error) {
      console.error('匯入商品失敗:', error);
      message.error(`匯入商品失敗：${error.message}`);
    }
  };

  // 匯入所有商品（包含未檢驗完成的）
  const importAllProducts = async () => {
    try {
      if (arrivalProducts.length === 0) {
        message.warning('沒有商品可以匯入');
        return;
      }

      // 調用後端API匯入所有商品
      const response = await fetch('http://localhost:5001/api/products/import', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ products: arrivalProducts, type: 'all' })
      });

      const result = await response.json();

      if (result.success) {
        message.success(`成功匯入 ${result.importedCount} 個商品至商品資料庫`);

        // 清空到貨商品列表
        setArrivalProducts([]);
      } else {
        throw new Error(result.message || '匯入失敗');
      }

    } catch (error) {
      console.error('匯入商品失敗:', error);
      message.error(`匯入商品失敗：${error.message}`);
    }
  };

  // 顯示匯入確認對話框
  const showImportModal = (type) => {
    setImportType(type);
    setImportModalVisible(true);
  };

  // 確認匯入
  const handleImportConfirm = () => {
    setImportModalVisible(false);
    if (importType === 'verified') {
      importVerifiedProducts();
    } else {
      importAllProducts();
    }
  };

  // 定義所有可用的欄位
  const allColumns = {
    upc: {
      title: 'UPC',
      dataIndex: 'upc',
      key: 'upc',
      width: 140,
      render: (upc) => (
        <div style={{ minWidth: '120px' }}>
          <Text code>{upc}</Text>
        </div>
      )
    },
    productName: {
      title: '商品名稱',
      dataIndex: 'productName',
      key: 'productName',
      width: 280,
      render: (text, record) => (
        <div style={{ minWidth: '260px' }}>
          <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>{text}</div>
          <div style={{ color: '#666', fontSize: '12px' }}>Artist: {record.artist}</div>
        </div>
      ),
    },
    specifications: {
      title: '規格',
      dataIndex: 'specifications',
      key: 'specifications',
      width: 160,
      render: (specs) => (
        <div style={{ minWidth: '140px' }}>
          {specs && Object.entries(specs).map(([key, value]) => (
            <Tag key={key} size="small" style={{ marginBottom: '2px' }}>{value}</Tag>
          ))}
        </div>
      ),
    },
    expectedQuantity: {
      title: '預期數量',
      dataIndex: 'expectedQuantity',
      key: 'expectedQuantity',
      width: 60,
      align: 'center',
    },
    actualQuantity: {
      title: '實際數量',
      dataIndex: 'actualQuantity',
      key: 'actualQuantity',
      width: 110,
      align: 'center',
      render: (actual, record) => {
        const color = actual === record.expectedQuantity ? '#52c41a' :
                     actual > record.expectedQuantity ? '#ff4d4f' : '#faad14';

        return (
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '4px' }}>
            <Tooltip title="減少數量">
              <Button
                type="text"
                size="small"
                icon={<MinusOutlined />}
                onClick={() => adjustQuantity(record.id, -1)}
                disabled={actual <= 0}
                style={{
                  width: '24px',
                  height: '24px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              />
            </Tooltip>

            <InputNumber
              value={actual}
              min={0}
              max={999}
              size="small"
              style={{
                width: '40px',
                textAlign: 'center'
              }}
              controls={false}
              onChange={(value) => handleQuantityChange(record.id, value || 0)}
              onPressEnter={(e) => e.target.blur()}
            />

            <Tooltip title="增加數量">
              <Button
                type="text"
                size="small"
                icon={<PlusOutlined />}
                onClick={() => adjustQuantity(record.id, 1)}
                style={{
                  width: '24px',
                  height: '24px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              />
            </Tooltip>

            <div style={{ marginLeft: '8px' }}>
              <Text style={{
                color,
                fontWeight: 'bold',
                fontSize: '12px'
              }}>
                /{record.expectedQuantity}
              </Text>
            </div>
          </div>
        );
      }
    },
    status: {
      title: '狀態',
      dataIndex: 'status',
      key: 'status',
      width: 60,
      render: (status) => (
        <div style={{ minWidth: '60px' }}>
          <Badge
            status={getStatusColor(status)}
            text={getStatusText(status)}
          />
        </div>
      ),
    },
    lastScannedAt: {
      title: '最後掃描',
      dataIndex: 'lastScannedAt',
      key: 'lastScannedAt',
      width: 120,
      render: (time) => (
        <div style={{ minWidth: '100px', fontSize: '12px' }}>
          {time ? new Date(time).toLocaleTimeString('zh-TW') : '-'}
        </div>
      ),
    }
  };

  // 獲取表格列定義
  const getTableColumns = () => {
    const columnOrder = ['status', 'expectedQuantity', 'actualQuantity', 'productName', 'upc', 'lastScannedAt'];
    return columnOrder.map(key => allColumns[key]);
  };

  const columns = getTableColumns();

  // Excel 上傳處理
  const handleExcelUpload = {
    beforeUpload: (file) => {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                     file.type === 'application/vnd.ms-excel';
      if (!isExcel) {
        message.error('只能上傳 Excel 檔案!');
        return false;
      }

      // 這裡應該處理 Excel 檔案解析
      message.success(`${file.name} 檔案上傳成功，正在處理...`);
      setUploadModalVisible(false);

      // 模擬處理延遲
      setTimeout(() => {
        message.success('Excel 檔案處理完成，商品資料已匯入');
      }, 2000);

      return false; // 阻止自動上傳
    },
  };

  return (
    <div style={{ padding: '0 24px' }}>
      {/* 頁面標題和操作按鈕 */}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0 }}>到貨商品檢驗</Title>
        <Space>
          <Button
            icon={<DatabaseOutlined />}
            onClick={() => showImportModal('verified')}
            disabled={arrivalProducts.filter(p => p.status === 'verified' && p.expectedQuantity === p.actualQuantity).length === 0}
          >
            匯入已檢驗商品
          </Button>
          <Button
            icon={<ImportOutlined />}
            onClick={() => showImportModal('all')}
            disabled={arrivalProducts.length === 0}
          >
            匯入所有商品至商品庫
          </Button>
          <Button
            type="primary"
            icon={<UploadOutlined />}
            onClick={() => setUploadModalVisible(true)}
          >
            匯入出貨單
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => window.location.reload()}
          >
            重新載入
          </Button>
        </Space>
      </div>

      {/* 統計資訊 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={4}>
          <Card size="small">
            <Statistic
              title="總商品數"
              value={stats.total}
              prefix={<InboxOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card size="small">
            <Statistic
              title="已完成"
              value={stats.completed}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card size="small">
            <Statistic
              title="檢驗中"
              value={stats.checking}
              prefix={<ScanOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card size="small">
            <Statistic
              title="有差異"
              value={stats.discrepancy}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card size="small">
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <div>
                <div style={{ fontSize: '14px', color: '#666' }}>檢驗進度</div>
                <div style={{ fontSize: '24px', fontWeight: 'bold' }}>
                  {stats.totalScanned} / {stats.totalExpected}
                </div>
              </div>
              <Progress
                type="circle"
                percent={Math.round(stats.progress)}
                size={60}
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068',
                }}
              />
            </div>
          </Card>
        </Col>
      </Row>

      {/* 主要內容區域 */}
      <Row gutter={[16, 16]} style={{ minHeight: '600px' }}>
        {/* 左側：出貨單資料表格 (65%) */}
        <Col span={15}>
          <Card
            title="出貨單商品清單"
            size="small"
            style={{ height: '100%' }}
            extra={
              <Badge count={stats.total} showZero>
                <span>商品總數</span>
              </Badge>
            }
          >
            <Table
              columns={columns}
              dataSource={arrivalProducts}
              rowKey="id"
              size="small"
              pagination={false}
              scroll={{
                x: 1000, // 設置橫向滾動的最小寬度
                y: 480   // 保持縱向滾動
              }}
              rowClassName={(record) => {
                if (record.status === 'completed') return 'row-completed';
                if (record.status === 'discrepancy') return 'row-discrepancy';
                if (record.status === 'checking') return 'row-checking';
                return '';
              }}
            />
          </Card>
        </Col>

        {/* 右側：掃碼區域 (35%) */}
        <Col span={9}>
          <div style={{ display: 'flex', flexDirection: 'column', height: '100%', gap: '16px' }}>

            {/* 掃碼輸入區 */}
            <Card title="掃碼輸入" size="small">
              <Input
                ref={scanInputRef}
                placeholder="請掃描商品條碼或手動輸入 UPC"
                value={scanInput}
                onChange={(e) => setScanInput(e.target.value)}
                onKeyDown={handleScanInput}
                prefix={<ScanOutlined />}
                size="large"
                autoFocus
              />
              <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
                掃描條碼後按 Enter 鍵確認
              </div>
            </Card>

            {/* 前個被掃描商品顯示區 */}
            {previousScannedProduct && (
              <Card title="前個被掃描商品" size="small" style={{ backgroundColor: '#f6ffed' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  {/* 商品圖片 */}
                  <Image
                    src={previousScannedProduct.productImage}
                    alt={previousScannedProduct.productName}
                    width={60}
                    height={60}
                    style={{
                      borderRadius: '4px',
                      border: '1px solid #d9d9d9'
                    }}
                    fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
                  />

                  {/* 商品資訊 */}
                  <div style={{ flex: 1, minWidth: 0 }}>
                    <div style={{ marginBottom: 4 }}>
                      <Text strong style={{ fontSize: '14px' }} ellipsis>
                        {previousScannedProduct.productName}
                      </Text>
                    </div>
                    <div style={{ marginBottom: 4 }}>
                      <Text style={{ fontSize: '12px', color: '#666' }}>
                        {previousScannedProduct.artist}
                      </Text>
                    </div>
                    <div>
                      <Text code style={{ fontSize: '12px' }}>
                        {previousScannedProduct.upc}
                      </Text>
                    </div>
                  </div>
                </div>
              </Card>
            )}

            {/* 掃碼結果顯示區 */}
            <Card
              title="掃碼結果"
              size="small"
              style={{ flex: 1 }}
              styles={{
                body: {
                  height: '400px',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center'
                }
              }}
            >
              {scannedProduct ? (
                <div style={{ textAlign: 'center', width: '100%' }}>
                  {/* 商品圖片 */}
                  <div style={{ marginBottom: 16 }}>
                    <Image
                      src={scannedProduct.productImage}
                      alt={scannedProduct.productName}
                      width={200}
                      height={200}
                      style={{
                        borderRadius: '8px',
                        border: scannedProduct.error ? '2px solid #ff4d4f' : '2px solid #52c41a',
                        padding: '8px'
                      }}
                      fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
                    />
                  </div>

                  {/* 商品資訊 */}
                  <div style={{ textAlign: 'left', width: '100%' }}>
                    <div style={{ marginBottom: 8 }}>
                      <Text strong style={{ fontSize: '16px' }}>
                        {scannedProduct.productName}
                      </Text>
                    </div>

                    <div style={{ marginBottom: 8 }}>
                      <Text style={{ fontSize: '14px', color: '#666' }}>
                        Artist: {scannedProduct.artist}
                      </Text>
                    </div>

                    <div style={{ marginBottom: 8 }}>
                      <Text code style={{ fontSize: '14px' }}>
                        UPC: {scannedProduct.upc}
                      </Text>
                    </div>

                    {scannedProduct.actualQuantity !== undefined && (
                      <div style={{ marginBottom: 8 }}>
                        <Text style={{ fontSize: '14px' }}>
                          數量: {scannedProduct.actualQuantity} / {scannedProduct.expectedQuantity}
                        </Text>
                      </div>
                    )}

                    {scannedProduct.lastScannedAt && (
                      <div style={{ marginBottom: 8 }}>
                        <Text style={{ fontSize: '12px', color: '#999' }}>
                          掃描時間: {new Date(scannedProduct.lastScannedAt).toLocaleString('zh-TW')}
                        </Text>
                      </div>
                    )}

                    {scannedProduct.error && (
                      <div style={{ marginTop: 16 }}>
                        <Tag color="red">找不到此商品</Tag>
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div style={{ textAlign: 'center', color: '#999' }}>
                  <ScanOutlined style={{ fontSize: '48px', marginBottom: 16 }} />
                  <div>請掃描商品條碼</div>
                </div>
              )}
            </Card>
          </div>
        </Col>
      </Row>

      {/* Excel 上傳模態框 */}
      <Modal
        title="匯入出貨單 Excel"
        open={uploadModalVisible}
        onCancel={() => setUploadModalVisible(false)}
        footer={null}
        width={600}
      >
        <div style={{ marginBottom: 16 }}>
          <Text>選擇供應商：</Text>
          <Select
            style={{ width: '100%', marginTop: 8 }}
            placeholder="請選擇供應商"
            value={selectedSupplier}
            onChange={setSelectedSupplier}
          >
            {suppliers.map(supplier => (
              <Option key={supplier} value={supplier}>{supplier}</Option>
            ))}
          </Select>
        </div>

        <Dragger {...handleExcelUpload}>
          <p className="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p className="ant-upload-text">點擊或拖拽 Excel 檔案到此區域上傳</p>
          <p className="ant-upload-hint">
            支援 .xlsx 和 .xls 格式的出貨單檔案
          </p>
        </Dragger>
      </Modal>

      {/* 匯入商品確認對話框 */}
      <Modal
        title={importType === 'verified' ? '匯入已檢驗完成商品' : '匯入所有商品至商品庫'}
        open={importModalVisible}
        onOk={handleImportConfirm}
        onCancel={() => setImportModalVisible(false)}
        okText="確認匯入"
        cancelText="取消"
        okButtonProps={{
          type: 'primary',
          icon: importType === 'verified' ? <DatabaseOutlined /> : <ImportOutlined />
        }}
      >
        {importType === 'verified' ? (
          <div>
            <p>
              <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />
              即將匯入 <strong>{arrivalProducts.filter(p => p.status === 'verified' && p.expectedQuantity === p.actualQuantity).length}</strong> 個已檢驗完成的商品到商品資料庫。
            </p>
            <p style={{ color: '#666', fontSize: '14px' }}>
              ✅ 只有預期數量 = 實際數量的商品會被匯入<br/>
              ✅ 匯入後這些商品將從到貨檢驗列表中移除<br/>
              ✅ 商品將出現在"所有商品"頁面中
            </p>
          </div>
        ) : (
          <div>
            <p>
              <ExclamationCircleOutlined style={{ color: '#faad14', marginRight: 8 }} />
              即將匯入 <strong>{arrivalProducts.length}</strong> 個商品（包含未檢驗完成的商品）到商品資料庫。
            </p>
            <p style={{ color: '#666', fontSize: '14px' }}>
              ⚠️ 包含所有商品，無論檢驗狀態<br/>
              ⚠️ 未檢驗完成的商品庫存數量將設為實際數量<br/>
              ✅ 匯入後所有商品將從到貨檢驗列表中移除<br/>
              ✅ 商品將出現在"所有商品"頁面中
            </p>
          </div>
        )}
      </Modal>

      {/* 自定義樣式 */}
      <style jsx>{`
        .row-completed {
          background-color: #f6ffed !important;
        }
        .row-discrepancy {
          background-color: #fff2f0 !important;
        }
        .row-checking {
          background-color: #e6f7ff !important;
        }
      `}</style>
    </div>
  );
}

export default ArrivalsPage;