import React from 'react';
import { Layout, Menu } from 'antd';
import { Link } from 'react-router-dom';
import {
  HomeOutlined,
  ShoppingCartOutlined,
  InboxOutlined,
  DatabaseOutlined,
  SwapOutlined,
  HistoryOutlined,
  ProductOutlined,
  AppstoreOutlined
} from '@ant-design/icons';

const { Sider } = Layout;

function Sidebar() {
  const menuItems = [
    {
      key: 'home',
      icon: <HomeOutlined />,
      label: <Link to="/">首頁</Link>,
    },
    {
      key: 'orders',
      icon: <ShoppingCartOutlined />,
      label: <Link to="/orders">所有訂單</Link>,
    },
    {
      key: 'arrivals',
      icon: <InboxOutlined />,
      label: <Link to="/arrivals">到貨商品</Link>,
    },
    {
      key: 'product-management',
      icon: <ProductOutlined />,
      label: '商品管理',
      children: [
        {
          key: 'all-products',
          icon: <AppstoreOutlined />,
          label: <Link to="/products">所有商品</Link>,
        },
        {
          key: 'inventory-management',
          icon: <DatabaseOutlined />,
          label: <Link to="/inventory">庫存管理</Link>,
        }
      ]
    },
    {
      key: 'matching',
      icon: <SwapOutlined />,
      label: <Link to="/matching">商品比對</Link>,
    },
    {
      key: 'history',
      icon: <HistoryOutlined />,
      label: <Link to="/history">分配歷史</Link>,
    },
  ];

  return (
    <Sider width={200} style={{ background: '#fff' }}>
      <div style={{ padding: '16px', textAlign: 'center', borderBottom: '1px solid #f0f0f0' }}>
        <h3>環球黑膠系統</h3>
      </div>
      <Menu
        mode="inline"
        defaultSelectedKeys={['home']}
        defaultOpenKeys={['product-management']}
        style={{ height: '100%', borderRight: 0 }}
        items={menuItems}
      />
    </Sider>
  );
}

export default Sidebar;