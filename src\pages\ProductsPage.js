import { useState, useEffect } from 'react';
import { 
  Table, 
  Card, 
  Row, 
  Col, 
  Button, 
  message, 
  Spin, 
  Tag,
  Select,
  Space,
  Typography,
  Image,
  Statistic,
  Modal,
  Descriptions,
  Switch,
  Dropdown
} from 'antd';
import {
  FilterOutlined,
  AppstoreOutlined,
  UnorderedListOutlined,
  ReloadOutlined,
  EyeOutlined,
  EditOutlined,
  SyncOutlined,
  CloudSyncOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { Option } = Select;

function ProductsPage() {
  const [loading, setLoading] = useState(false);
  const [products, setProducts] = useState([]);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [viewMode, setViewMode] = useState('list'); // 'list' or 'grid'
  const [filterVisible, setFilterVisible] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [productDetailVisible, setProductDetailVisible] = useState(false);
  
  // 篩選條件
  const [filters, setFilters] = useState({
    publishStatus: 'all', // all, published, unpublished
    category: 'all', // all, assigned, unassigned
    brand: 'all',
    supplier: 'all',
    tags: 'all'
  });

  // 模擬商品數據
  const mockProducts = [
    {
      id: 1,
      productId: '0656605310817',
      productName: 'Pink Floyd - The Dark Side of the Moon (Vinyl)',
      sku: 'VINYL-PF-DSOTM-001',
      upc: '0656605310817',
      artist: 'Pink Floyd',
      brand: 'B2B Company Ltd.',
      supplier: 'AMI',
      category: 'Vinyl Records',
      price: 850.00,
      stockQuantity: 15,
      isPublished: true,
      productImages: [
        'https://via.placeholder.com/300x300/000000/FFFFFF?text=Pink+Floyd+DSOTM'
      ],
      tags: ['代購', '經典', '搖滾'],
      status: 'active'
    },
    {
      id: 2,
      productId: '081227971816',
      productName: 'Led Zeppelin - IV (Vinyl)',
      sku: 'VINYL-LZ-IV-001',
      upc: '081227971816',
      artist: 'Led Zeppelin',
      brand: 'World K-POP Center',
      supplier: 'MONO',
      category: 'Vinyl Records',
      price: 750.00,
      stockQuantity: 8,
      isPublished: true,
      productImages: [
        'https://via.placeholder.com/300x300/8B4513/FFFFFF?text=Led+Zeppelin+IV'
      ],
      tags: ['搖滾', '經典', '韓國'],
      status: 'active'
    },
    {
      id: 3,
      productId: '094638241614',
      productName: 'The Beatles - Abbey Road (50th Anniversary Edition)',
      sku: 'VINYL-BEATLES-AR-50TH',
      upc: '094638241614',
      artist: 'The Beatles',
      brand: 'Avex',
      supplier: 'SK1',
      category: null,
      price: 1200.00,
      stockQuantity: 0,
      isPublished: false,
      productImages: [
        'https://via.placeholder.com/300x300/4169E1/FFFFFF?text=Beatles+Abbey+Road'
      ],
      tags: ['粉專排隊', '即將發行', '限量版'],
      status: 'active'
    }
  ];

  // 從後端API獲取商品數據
  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:5000/api/products');
      const data = await response.json();

      setProducts(data);
      setFilteredProducts(data);
      message.success(`成功載入 ${data.length} 個商品`);
    } catch (error) {
      console.error('獲取商品數據失敗:', error);
      message.error('獲取商品數據失敗');
    } finally {
      setLoading(false);
    }
  };

  // 從 EasyStore 同步商品
  const syncEasyStoreProducts = async () => {
    try {
      setLoading(true);
      message.loading('正在從 EasyStore 同步商品...', 0);

      const response = await fetch('http://localhost:5000/api/products/sync-easystore', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();
      message.destroy(); // 清除 loading 訊息

      if (result.success) {
        message.success(result.message);
        // 同步完成後重新載入商品列表
        await fetchProducts();
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      message.destroy();
      console.error('EasyStore 商品同步失敗:', error);
      message.error(`EasyStore 商品同步失敗：${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProducts();
  }, []);

  // 應用篩選條件
  const applyFilters = () => {
    let filtered = [...products];

    // 上架狀態篩選
    if (filters.publishStatus !== 'all') {
      filtered = filtered.filter(product => {
        if (filters.publishStatus === 'published') return product.isPublished;
        if (filters.publishStatus === 'unpublished') return !product.isPublished;
        return true;
      });
    }

    // 商品分類篩選
    if (filters.category !== 'all') {
      filtered = filtered.filter(product => {
        if (filters.category === 'assigned') return product.category !== null;
        if (filters.category === 'unassigned') return product.category === null;
        return true;
      });
    }

    // 品牌篩選
    if (filters.brand !== 'all') {
      filtered = filtered.filter(product => product.brand === filters.brand);
    }

    // 供應商篩選
    if (filters.supplier !== 'all') {
      filtered = filtered.filter(product => product.supplier === filters.supplier);
    }

    // 標籤篩選
    if (filters.tags !== 'all') {
      filtered = filtered.filter(product => 
        product.tags && product.tags.includes(filters.tags)
      );
    }

    setFilteredProducts(filtered);
    message.success(`篩選完成，找到 ${filtered.length} 個商品`);
  };

  // 重置篩選條件
  const resetFilters = () => {
    setFilters({
      publishStatus: 'all',
      category: 'all',
      brand: 'all',
      supplier: 'all',
      tags: 'all'
    });
    setFilteredProducts(products);
    message.success('篩選條件已重置');
  };

  // 查看商品詳情
  const viewProductDetail = (product) => {
    setSelectedProduct(product);
    setProductDetailVisible(true);
  };

  // 篩選器下拉選單
  const filterMenu = {
    items: [
      {
        key: 'apply',
        label: '應用篩選',
        onClick: applyFilters
      },
      {
        key: 'reset',
        label: '重置篩選',
        onClick: resetFilters
      }
    ]
  };

  // 列表模式的表格列定義
  const columns = [
    {
      title: '商品圖片',
      dataIndex: 'productImages',
      key: 'productImages',
      width: 80,
      render: (images) => (
        <Image
          src={images && images[0] ? images[0] : 'https://via.placeholder.com/60x60'}
          alt="商品圖片"
          width={60}
          height={60}
          style={{ borderRadius: '4px' }}
        />
      ),
    },
    {
      title: '商品名稱',
      dataIndex: 'productName',
      key: 'productName',
      width: 300,
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>{text}</div>
          <div style={{ color: '#666', fontSize: '12px' }}>
            Artist: {record.artist} | SKU: {record.sku}
          </div>
        </div>
      ),
    },
    {
      title: '庫存數量',
      dataIndex: 'stockQuantity',
      key: 'stockQuantity',
      width: 100,
      align: 'center',
      render: (quantity) => (
        <Text style={{ 
          color: quantity > 0 ? '#52c41a' : '#ff4d4f',
          fontWeight: 'bold'
        }}>
          {quantity}
        </Text>
      ),
    },
    {
      title: '上架狀態',
      dataIndex: 'isPublished',
      key: 'isPublished',
      width: 100,
      align: 'center',
      render: (isPublished) => (
        <Tag color={isPublished ? 'green' : 'red'}>
          {isPublished ? '已上架' : '未上架'}
        </Tag>
      ),
    },
    {
      title: '價格',
      dataIndex: 'price',
      key: 'price',
      width: 120,
      align: 'right',
      render: (price) => `NT$${price.toLocaleString()}`,
    },
    {
      title: '品牌',
      dataIndex: 'brand',
      key: 'brand',
      width: 150,
    },
    {
      title: '供應商',
      dataIndex: 'supplier',
      key: 'supplier',
      width: 100,
    },
    {
      title: '分類',
      dataIndex: 'category',
      key: 'category',
      width: 120,
      render: (category) => category || <Text type="secondary">未分配</Text>,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => viewProductDetail(record)}
          />
          <Button
            type="text"
            icon={<EditOutlined />}
          />
        </Space>
      ),
    },
  ];

  // 計算統計數據
  const getStats = () => {
    const total = filteredProducts.length;
    const published = filteredProducts.filter(p => p.isPublished).length;
    const unpublished = filteredProducts.filter(p => !p.isPublished).length;
    const inStock = filteredProducts.filter(p => p.stockQuantity > 0).length;
    const outOfStock = filteredProducts.filter(p => p.stockQuantity === 0).length;

    return { total, published, unpublished, inStock, outOfStock };
  };

  const stats = getStats();

  return (
    <div style={{ padding: '0 24px' }}>
      {/* 頁面標題和操作按鈕 */}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0 }}>所有商品</Title>
        <Space>
          <Dropdown menu={filterMenu} trigger={['click']}>
            <Button icon={<FilterOutlined />}>
              過濾器
            </Button>
          </Dropdown>
          <Button.Group>
            <Button
              type={viewMode === 'list' ? 'primary' : 'default'}
              icon={<UnorderedListOutlined />}
              onClick={() => setViewMode('list')}
            >
              列表
            </Button>
            <Button
              type={viewMode === 'grid' ? 'primary' : 'default'}
              icon={<AppstoreOutlined />}
              onClick={() => setViewMode('grid')}
            >
              圖片
            </Button>
          </Button.Group>
          <Button
            icon={<CloudSyncOutlined />}
            type="primary"
            onClick={syncEasyStoreProducts}
            loading={loading}
          >
            從 EasyStore 同步商品
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={fetchProducts}
            loading={loading}
          >
            重新載入
          </Button>
        </Space>
      </div>

      {/* 篩選條件面板 */}
      <Card size="small" style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col span={3}>
            <Text strong>篩選條件:</Text>
          </Col>
          <Col span={3}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Text type="secondary">上架狀態</Text>
              <Select
                value={filters.publishStatus}
                onChange={(value) => setFilters({...filters, publishStatus: value})}
                style={{ width: '100%' }}
                size="small"
              >
                <Option value="all">全部</Option>
                <Option value="published">已上架</Option>
                <Option value="unpublished">未上架</Option>
              </Select>
            </Space>
          </Col>
          <Col span={3}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Text type="secondary">商品分類</Text>
              <Select
                value={filters.category}
                onChange={(value) => setFilters({...filters, category: value})}
                style={{ width: '100%' }}
                size="small"
              >
                <Option value="all">全部</Option>
                <Option value="assigned">已分配</Option>
                <Option value="unassigned">未分配</Option>
              </Select>
            </Space>
          </Col>
          <Col span={3}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Text type="secondary">商品品牌</Text>
              <Select
                value={filters.brand}
                onChange={(value) => setFilters({...filters, brand: value})}
                style={{ width: '100%' }}
                size="small"
              >
                <Option value="all">全部</Option>
                <Option value="B2B Company Ltd.">B2B Company Ltd.</Option>
                <Option value="World K-POP Center">World K-POP Center</Option>
                <Option value="Avex">Avex</Option>
              </Select>
            </Space>
          </Col>
          <Col span={3}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Text type="secondary">供應商</Text>
              <Select
                value={filters.supplier}
                onChange={(value) => setFilters({...filters, supplier: value})}
                style={{ width: '100%' }}
                size="small"
              >
                <Option value="all">全部</Option>
                <Option value="AMI">AMI</Option>
                <Option value="MONO">MONO</Option>
                <Option value="SK1">SK1</Option>
              </Select>
            </Space>
          </Col>
          <Col span={3}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Text type="secondary">商品標籤</Text>
              <Select
                value={filters.tags}
                onChange={(value) => setFilters({...filters, tags: value})}
                style={{ width: '100%' }}
                size="small"
              >
                <Option value="all">全部</Option>
                <Option value="代購">代購</Option>
                <Option value="韓國">韓國</Option>
                <Option value="粉專排隊">粉專排隊</Option>
                <Option value="即將發行">即將發行</Option>
              </Select>
            </Space>
          </Col>
          <Col span={3}>
            <Space>
              <Button type="primary" size="small" onClick={applyFilters}>
                應用
              </Button>
              <Button size="small" onClick={resetFilters}>
                重置
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 統計資訊 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={4}>
          <Card size="small">
            <Statistic
              title="總商品數"
              value={stats.total}
              prefix={<AppstoreOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card size="small">
            <Statistic
              title="已上架"
              value={stats.published}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card size="small">
            <Statistic
              title="未上架"
              value={stats.unpublished}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card size="small">
            <Statistic
              title="有庫存"
              value={stats.inStock}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card size="small">
            <Statistic
              title="缺貨"
              value={stats.outOfStock}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 商品列表/網格 */}
      {viewMode === 'list' ? (
        <Card>
          <Table
            columns={columns}
            dataSource={filteredProducts}
            rowKey="id"
            pagination={{
              pageSize: 20,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 項，共 ${total} 項`
            }}
            scroll={{ x: 1400 }}
          />
        </Card>
      ) : (
        <Row gutter={[16, 16]}>
          {filteredProducts.map(product => (
            <Col key={product.id} xs={24} sm={12} md={8} lg={6} xl={4}>
              <Card
                hoverable
                cover={
                  <Image
                    src={product.productImages && product.productImages[0] ?
                         product.productImages[0] :
                         'https://via.placeholder.com/300x300'}
                    alt={product.productName}
                    height={200}
                    style={{ objectFit: 'cover' }}
                  />
                }
                actions={[
                  <EyeOutlined key="view" onClick={() => viewProductDetail(product)} />,
                  <EditOutlined key="edit" />
                ]}
              >
                <Card.Meta
                  title={
                    <div style={{ height: '40px', overflow: 'hidden' }}>
                      <Text ellipsis={{ tooltip: product.productName }}>
                        {product.productName}
                      </Text>
                    </div>
                  }
                  description={
                    <div>
                      <div style={{ marginBottom: '8px' }}>
                        <Text strong style={{ fontSize: '16px', color: '#1890ff' }}>
                          NT${product.price.toLocaleString()}
                        </Text>
                      </div>
                      <div style={{ marginBottom: '8px' }}>
                        <Tag color={product.isPublished ? 'green' : 'red'}>
                          {product.isPublished ? '已上架' : '未上架'}
                        </Tag>
                        <Tag color={product.stockQuantity > 0 ? 'blue' : 'red'}>
                          庫存: {product.stockQuantity}
                        </Tag>
                      </div>
                      <div>
                        {product.tags && product.tags.slice(0, 2).map(tag => (
                          <Tag key={tag} size="small">{tag}</Tag>
                        ))}
                      </div>
                    </div>
                  }
                />
              </Card>
            </Col>
          ))}
        </Row>
      )}

      {/* 商品詳情模態框 */}
      <Modal
        title={`商品詳情 - ${selectedProduct?.productName}`}
        open={productDetailVisible}
        onCancel={() => setProductDetailVisible(false)}
        footer={null}
        width={800}
      >
        {selectedProduct && (
          <div>
            <Row gutter={16}>
              <Col span={8}>
                <Image
                  src={selectedProduct.productImages && selectedProduct.productImages[0] ?
                       selectedProduct.productImages[0] :
                       'https://via.placeholder.com/300x300'}
                  alt={selectedProduct.productName}
                  width="100%"
                />
              </Col>
              <Col span={16}>
                <Descriptions bordered column={1}>
                  <Descriptions.Item label="商品名稱">{selectedProduct.productName}</Descriptions.Item>
                  <Descriptions.Item label="藝術家">{selectedProduct.artist}</Descriptions.Item>
                  <Descriptions.Item label="SKU">{selectedProduct.sku}</Descriptions.Item>
                  <Descriptions.Item label="UPC">{selectedProduct.upc}</Descriptions.Item>
                  <Descriptions.Item label="品牌">{selectedProduct.brand}</Descriptions.Item>
                  <Descriptions.Item label="供應商">{selectedProduct.supplier}</Descriptions.Item>
                  <Descriptions.Item label="分類">{selectedProduct.category || '未分配'}</Descriptions.Item>
                  <Descriptions.Item label="價格">NT${selectedProduct.price.toLocaleString()}</Descriptions.Item>
                  <Descriptions.Item label="庫存數量">{selectedProduct.stockQuantity}</Descriptions.Item>
                  <Descriptions.Item label="上架狀態">
                    <Tag color={selectedProduct.isPublished ? 'green' : 'red'}>
                      {selectedProduct.isPublished ? '已上架' : '未上架'}
                    </Tag>
                  </Descriptions.Item>
                  <Descriptions.Item label="標籤">
                    {selectedProduct.tags && selectedProduct.tags.map(tag => (
                      <Tag key={tag}>{tag}</Tag>
                    ))}
                  </Descriptions.Item>
                </Descriptions>
              </Col>
            </Row>
          </div>
        )}
      </Modal>
    </div>
  );
}

export default ProductsPage;
