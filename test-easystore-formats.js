const axios = require('axios');
require('dotenv').config();

/**
 * 根據 EasyStore 官方文檔測試正確的 API 格式
 * EasyStore API 通常需要商店子域名，格式如：
 * https://{store_name}.api.easystore.co/api/3.0/orders
 */

async function testEasyStoreFormats() {
  const apiKey = process.env.EASYSTORE_API_KEY;
  const storeId = '18506'; // 從您的 URL 中提取的商店ID
  
  console.log('=== 測試 EasyStore API 正確格式 ===');
  console.log('商店ID:', storeId);
  console.log('API Key:', apiKey ? `${apiKey.substring(0, 8)}...` : '未設置');
  
  // 根據 EasyStore 文檔，正確的格式可能是：
  const testFormats = [
    // 格式1: 使用商店子域名
    `https://store${storeId}.api.easystore.co/api/3.0/orders`,
    `https://${storeId}.api.easystore.co/api/3.0/orders`,
    
    // 格式2: 在路徑中包含商店ID
    `https://api.easystore.co/api/3.0/stores/${storeId}/orders`,
    `https://api.easystore.co/api/v3/stores/${storeId}/orders`,
    
    // 格式3: 使用查詢參數
    `https://api.easystore.co/api/3.0/orders?store_id=${storeId}`,
    `https://api.easystore.co/api/3.0/orders?shop_id=${storeId}`,
    
    // 格式4: 嘗試不同的認證方式
    `https://api.easystore.co/api/3.0/orders`,
  ];
  
  for (let i = 0; i < testFormats.length; i++) {
    const url = testFormats[i];
    console.log(`\n--- 測試格式 ${i + 1}: ${url} ---`);
    
    // 嘗試不同的認證方式
    const authMethods = [
      // Bearer token
      {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      // API Key in header
      {
        'X-API-Key': apiKey,
        'Content-Type': 'application/json'
      },
      // API Key with store ID
      {
        'Authorization': `Bearer ${apiKey}`,
        'X-Store-ID': storeId,
        'Content-Type': 'application/json'
      },
      // Basic auth (API key as username)
      {
        'Authorization': `Basic ${Buffer.from(apiKey + ':').toString('base64')}`,
        'Content-Type': 'application/json'
      }
    ];
    
    for (let j = 0; j < authMethods.length; j++) {
      const headers = authMethods[j];
      console.log(`  認證方式 ${j + 1}:`, Object.keys(headers).join(', '));
      
      try {
        const response = await axios.get(url, {
          headers,
          timeout: 10000
        });
        
        console.log('  ✅ 成功!');
        console.log('  狀態碼:', response.status);
        console.log('  響應數據類型:', typeof response.data);
        
        if (response.data) {
          console.log('  響應數據結構:');
          if (typeof response.data === 'object') {
            console.log('  ', Object.keys(response.data));
          }
          console.log('  完整響應:');
          console.log(JSON.stringify(response.data, null, 2));
        }
        
        // 成功找到正確格式
        console.log('\n🎉 找到正確的 API 格式!');
        console.log('URL:', url);
        console.log('Headers:', headers);
        
        return { url, headers, data: response.data };
        
      } catch (error) {
        const status = error.response?.status;
        const message = error.response?.data?.error?.message || error.message;
        console.log(`  ❌ 失敗 (${status}): ${message}`);
        
        // 如果是 401 錯誤，說明端點正確但認證有問題
        if (status === 401) {
          console.log('  ⚠️ 認證失敗，但端點可能正確');
        }
        // 如果是 403 錯誤，說明認證成功但權限不足
        if (status === 403) {
          console.log('  ⚠️ 權限不足，請檢查 API Key 權限');
        }
      }
    }
  }
  
  console.log('\n=== 所有格式都測試完畢 ===');
  console.log('如果所有測試都失敗，請：');
  console.log('1. 檢查您的 EasyStore 後台，確認 API Key 設置');
  console.log('2. 確認商店ID是否正確');
  console.log('3. 檢查 API Key 是否有訂單讀取權限');
  console.log('4. 聯繫 EasyStore 技術支持確認正確的 API 格式');
  
  return null;
}

// 如果找到正確格式，測試數據庫保存
async function testWithCorrectFormat() {
  try {
    const result = await testEasyStoreFormats();
    
    if (result && result.data) {
      console.log('\n=== 開始測試數據庫保存 ===');
      // 這裡可以調用之前的數據庫保存邏輯
      console.log('數據庫測試將在下一步進行...');
    }
  } catch (error) {
    console.error('測試過程中出錯:', error);
  }
}

// 執行測試
testWithCorrectFormat();
