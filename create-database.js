const { Client } = require('pg');
require('dotenv').config();

/**
 * 創建 PostgreSQL 數據庫
 */

async function createDatabase() {
  console.log('=== 創建 PostgreSQL 數據庫 ===');
  
  const dbName = process.env.DB_NAME;
  const dbUser = process.env.DB_USER;
  const dbHost = process.env.DB_HOST;
  const dbPort = process.env.DB_PORT;
  const dbPass = process.env.DB_PASS;
  
  console.log('數據庫名稱:', dbName);
  console.log('用戶:', dbUser);
  console.log('主機:', dbHost);
  console.log('端口:', dbPort);
  
  // 首先連接到 postgres 默認數據庫來創建新數據庫
  const client = new Client({
    host: dbHost,
    port: dbPort,
    user: dbUser,
    password: dbPass,
    database: 'postgres' // 連接到默認的 postgres 數據庫
  });
  
  try {
    console.log('\n正在連接到 PostgreSQL...');
    await client.connect();
    console.log('✅ 成功連接到 PostgreSQL');
    
    // 檢查數據庫是否已存在
    console.log(`\n檢查數據庫 "${dbName}" 是否存在...`);
    const checkResult = await client.query(
      'SELECT 1 FROM pg_database WHERE datname = $1',
      [dbName]
    );
    
    if (checkResult.rows.length > 0) {
      console.log(`⚠️ 數據庫 "${dbName}" 已經存在`);
    } else {
      console.log(`數據庫 "${dbName}" 不存在，正在創建...`);
      
      // 創建數據庫
      await client.query(`CREATE DATABASE "${dbName}"`);
      console.log(`✅ 成功創建數據庫 "${dbName}"`);
    }
    
    console.log('\n=== 數據庫創建完成 ===');
    console.log('現在可以運行應用程序了');
    
  } catch (error) {
    console.error('\n❌ 創建數據庫失敗:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('\n連接被拒絕，請檢查：');
      console.error('1. PostgreSQL 服務是否正在運行');
      console.error('2. 端口 5432 是否正確');
      console.error('3. 主機地址是否正確');
    } else if (error.code === '28P01') {
      console.error('\n認證失敗，請檢查：');
      console.error('1. 用戶名是否正確');
      console.error('2. 密碼是否正確');
    } else if (error.code === '42P04') {
      console.error('\n數據庫已存在');
    } else {
      console.error('詳細錯誤:', error);
    }
  } finally {
    await client.end();
    console.log('\n數據庫連接已關閉');
  }
}

// 執行創建數據庫
createDatabase().catch(console.error);
