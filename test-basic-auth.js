const axios = require('axios');
require('dotenv').config();

/**
 * 測試基本的 EasyStore API 認證和端點發現
 */

async function testBasicAuth() {
  const apiKey = process.env.EASYSTORE_API_KEY;
  
  console.log('=== EasyStore API 基本認證測試 ===');
  console.log('API Key:', apiKey ? `${apiKey.substring(0, 8)}...` : '未設置');
  
  // 首先測試根端點，看看是否能獲得更多信息
  console.log('\n--- 測試根端點 ---');
  try {
    const response = await axios.get('https://api.easystore.co', {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });
    
    console.log('根端點響應:', response.status);
    console.log('響應數據:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.log('根端點失敗:', error.response?.status, error.response?.data || error.message);
  }
  
  // 測試可能的認證端點
  console.log('\n--- 測試認證端點 ---');
  const authEndpoints = [
    'https://api.easystore.co/auth',
    'https://api.easystore.co/api/auth',
    'https://api.easystore.co/api/3.0/auth',
    'https://api.easystore.co/oauth/token'
  ];
  
  for (const endpoint of authEndpoints) {
    try {
      console.log(`測試: ${endpoint}`);
      const response = await axios.get(endpoint, {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: 5000
      });
      
      console.log('✅ 成功:', response.status);
      console.log('響應:', JSON.stringify(response.data, null, 2));
    } catch (error) {
      console.log('❌ 失敗:', error.response?.status || 'Network Error');
    }
  }
  
  // 測試可能的商店信息端點
  console.log('\n--- 測試商店信息端點 ---');
  const storeEndpoints = [
    'https://api.easystore.co/api/3.0/store',
    'https://api.easystore.co/api/3.0/shop',
    'https://api.easystore.co/api/3.0/me',
    'https://api.easystore.co/api/3.0/account'
  ];
  
  for (const endpoint of storeEndpoints) {
    try {
      console.log(`測試: ${endpoint}`);
      const response = await axios.get(endpoint, {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: 5000
      });
      
      console.log('✅ 成功:', response.status);
      console.log('響應:', JSON.stringify(response.data, null, 2));
      
      // 如果成功，可能包含商店信息，可以幫助我們找到正確的訂單端點
      if (response.data && response.data.id) {
        console.log('找到商店ID:', response.data.id);
        await testOrdersWithStoreId(response.data.id, apiKey);
      }
      
    } catch (error) {
      console.log('❌ 失敗:', error.response?.status || 'Network Error');
    }
  }
  
  // 嘗試直接使用原始 URL 但使用不同的方法
  console.log('\n--- 測試原始 URL 的不同方法 ---');
  const originalUrl = process.env.EASYSTORE_API_URL;
  
  try {
    // 嘗試 POST 方法
    console.log('嘗試 POST 方法...');
    const postResponse = await axios.post(originalUrl, {}, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });
    
    console.log('✅ POST 成功:', postResponse.status);
    console.log('響應:', JSON.stringify(postResponse.data, null, 2));
    
  } catch (error) {
    console.log('❌ POST 失敗:', error.response?.status, error.response?.data?.error?.message || error.message);
  }
  
  // 檢查是否需要特殊的 User-Agent 或其他 headers
  console.log('\n--- 測試特殊 Headers ---');
  try {
    const response = await axios.get(originalUrl, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'User-Agent': 'EasyStore-API-Client/1.0',
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      },
      timeout: 10000
    });
    
    console.log('✅ 特殊 Headers 成功:', response.status);
    console.log('響應:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.log('❌ 特殊 Headers 失敗:', error.response?.status, error.response?.data?.error?.message || error.message);
  }
}

async function testOrdersWithStoreId(storeId, apiKey) {
  console.log(`\n--- 使用商店ID ${storeId} 測試訂單端點 ---`);
  
  const orderEndpoints = [
    `https://api.easystore.co/api/3.0/stores/${storeId}/orders`,
    `https://api.easystore.co/api/3.0/shop/${storeId}/orders`,
    `https://${storeId}.api.easystore.co/api/3.0/orders`
  ];
  
  for (const endpoint of orderEndpoints) {
    try {
      console.log(`測試: ${endpoint}`);
      const response = await axios.get(endpoint, {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });
      
      console.log('✅ 訂單端點成功:', response.status);
      console.log('響應:', JSON.stringify(response.data, null, 2));
      return endpoint; // 返回成功的端點
      
    } catch (error) {
      console.log('❌ 失敗:', error.response?.status || 'Network Error');
    }
  }
  
  return null;
}

// 執行測試
testBasicAuth().catch(console.error);
