const sequelize = require('./src/config/database');
const ProductInfo = require('./src/models/ProductInfo');
require('dotenv').config();

/**
 * 創建測試商品資料，對應 product_img 資料夾中的圖片
 */

const testProducts = [
  {
    productId: 'VINYL-DB-ZIGGY-001',
    productName: '<PERSON> - The Rise and Fall of Ziggy Stardust',
    sku: 'VINYL-DB-ZIGGY-001',
    upc: '0190295851418',
    artist: '<PERSON>',
    brand: 'Parlophone',
    supplier: 'AMI',
    category: 'Vinyl Records',
    price: 950.00,
    stockQuantity: 12,
    isPublished: true,
    tags: ['經典', '搖滾', '代購'],
    status: 'active',
    description: '<PERSON> 經典專輯《The Rise and Fall of Ziggy Stardust and the Spiders from Mars》黑膠唱片'
  },
  {
    productId: 'VINYL-LZ-IV-001',
    productName: 'Led Zeppelin - IV',
    sku: 'VINYL-LZ-IV-001',
    upc: '081227971816',
    artist: 'Led Zeppelin',
    brand: 'Atlantic Records',
    supplier: 'MONO',
    category: 'Vinyl Records',
    price: 750.00,
    stockQuantity: 8,
    isPublished: true,
    tags: ['搖滾', '經典', '韓國'],
    status: 'active',
    description: 'Led Zeppelin 第四張專輯，包含經典歌曲 "Stairway to Heaven"'
  },
  {
    productId: 'VINYL-PF-DSOTM-001',
    productName: 'Pink Floyd - The Dark Side of the Moon',
    sku: 'VINYL-PF-DSOTM-001',
    upc: '0656605310817',
    artist: 'Pink Floyd',
    brand: 'Harvest Records',
    supplier: 'AMI',
    category: 'Vinyl Records',
    price: 850.00,
    stockQuantity: 15,
    isPublished: true,
    tags: ['代購', '經典', '搖滾'],
    status: 'active',
    description: 'Pink Floyd 經典專輯《The Dark Side of the Moon》50週年紀念版黑膠唱片'
  },
  {
    productId: 'VINYL-QUEEN-BR-001',
    productName: 'Queen - Bohemian Rhapsody',
    sku: 'VINYL-QUEEN-BR-001',
    upc: '0602577208614',
    artist: 'Queen',
    brand: 'Hollywood Records',
    supplier: 'SK1',
    category: 'Vinyl Records',
    price: 680.00,
    stockQuantity: 20,
    isPublished: true,
    tags: ['經典', '搖滾', '電影原聲帶'],
    status: 'active',
    description: 'Queen 經典歌曲《Bohemian Rhapsody》單曲黑膠唱片'
  },
  {
    productId: 'VINYL-BEATLES-AR-001',
    productName: 'The Beatles - Abbey Road',
    sku: 'VINYL-BEATLES-AR-001',
    upc: '094638241614',
    artist: 'The Beatles',
    brand: 'Apple Records',
    supplier: 'SK1',
    category: 'Vinyl Records',
    price: 1200.00,
    stockQuantity: 5,
    isPublished: true,
    tags: ['粉專排隊', '經典', '限量版'],
    status: 'active',
    description: 'The Beatles 經典專輯《Abbey Road》50週年紀念版黑膠唱片'
  }
];

async function createTestProducts() {
  console.log('=== 創建測試商品資料 ===');
  
  try {
    // 測試資料庫連接
    await sequelize.authenticate();
    console.log('✅ 資料庫連接成功');
    
    // 同步資料庫模型
    await sequelize.sync();
    console.log('✅ 資料庫模型同步成功');
    
    console.log('\n=== 檢查現有商品 ===');
    const existingProducts = await ProductInfo.findAll();
    console.log(`目前資料庫中有 ${existingProducts.length} 個商品`);
    
    if (existingProducts.length > 0) {
      console.log('現有商品:');
      existingProducts.forEach(product => {
        console.log(`  - ${product.productName} (ID: ${product.productId})`);
      });
    }
    
    console.log('\n=== 創建測試商品 ===');
    let createdCount = 0;
    let skippedCount = 0;
    
    for (const productData of testProducts) {
      try {
        // 檢查商品是否已存在
        const existingProduct = await ProductInfo.findOne({
          where: { productId: productData.productId }
        });
        
        if (existingProduct) {
          console.log(`⚠️  商品已存在，跳過: ${productData.productName}`);
          skippedCount++;
          continue;
        }
        
        // 創建新商品
        const newProduct = await ProductInfo.create(productData);
        console.log(`✅ 創建商品: ${newProduct.productName} (ID: ${newProduct.id})`);
        createdCount++;
        
      } catch (error) {
        console.error(`❌ 創建商品失敗: ${productData.productName}`, error.message);
      }
    }
    
    console.log('\n=== 創建完成 ===');
    console.log(`✅ 成功創建: ${createdCount} 個商品`);
    console.log(`⚠️  跳過已存在: ${skippedCount} 個商品`);
    
    // 顯示最終結果
    console.log('\n=== 最終商品列表 ===');
    const finalProducts = await ProductInfo.findAll({
      order: [['createdAt', 'DESC']]
    });
    
    console.log(`資料庫中共有 ${finalProducts.length} 個商品:`);
    finalProducts.forEach((product, index) => {
      console.log(`${index + 1}. ${product.productName}`);
      console.log(`   - SKU: ${product.sku}`);
      console.log(`   - 藝術家: ${product.artist}`);
      console.log(`   - 價格: NT$${product.price}`);
      console.log(`   - 庫存: ${product.stockQuantity}`);
      console.log(`   - 狀態: ${product.isPublished ? '已上架' : '未上架'}`);
      console.log('');
    });
    
  } catch (error) {
    console.error('❌ 創建測試商品失敗:', error.message);
    console.error('詳細錯誤:', error);
  } finally {
    await sequelize.close();
    console.log('資料庫連接已關閉');
  }
}

// 執行創建
createTestProducts().catch(console.error);
