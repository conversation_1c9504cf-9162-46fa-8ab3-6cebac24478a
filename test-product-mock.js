const sequelize = require('./src/config/database');
const ProductInfo = require('./src/models/ProductInfo');
require('dotenv').config();

/**
 * 創建模擬商品數據並測試數據庫
 */
async function testProductDatabase() {
  console.log('=== 測試商品數據庫功能 ===');
  
  try {
    // 測試數據庫連接
    console.log('正在測試數據庫連接...');
    await sequelize.authenticate();
    console.log('✅ 數據庫連接成功');
    
    // 同步數據庫模型
    console.log('正在同步數據庫模型...');
    await sequelize.sync();
    console.log('✅ 數據庫模型同步成功');
    
    // 創建模擬商品數據
    const mockProducts = [
      {
        productId: '0656605310817',
        productName: 'Pink Floyd - The Dark Side of the Moon (Vinyl)',
        sku: 'VINYL-PF-DSOTM-001',
        upc: '0656605310817',
        artist: '<PERSON> Floyd',
        brand: 'B2B Company Ltd.',
        supplier: 'AMI',
        category: 'Vinyl Records',
        price: 850.00,
        costPrice: 600.00,
        stockQuantity: 15,
        reservedQuantity: 3,
        availableQuantity: 12,
        isPublished: true,
        publishedAt: new Date('2024-01-15'),
        productImages: [
          'https://via.placeholder.com/400x400/000000/FFFFFF?text=Pink+Floyd+DSOTM',
          'https://via.placeholder.com/400x400/8B4513/FFFFFF?text=Back+Cover'
        ],
        productVariants: [
          {
            id: 1,
            title: 'Black Vinyl',
            price: 850.00,
            sku: 'VINYL-PF-DSOTM-BLACK',
            inventory_quantity: 10
          },
          {
            id: 2,
            title: 'Limited Edition Clear Vinyl',
            price: 1200.00,
            sku: 'VINYL-PF-DSOTM-CLEAR',
            inventory_quantity: 5
          }
        ],
        tags: ['代購', '經典', '搖滾'],
        description: 'Pink Floyd 經典專輯《月之暗面》黑膠唱片，1973年發行的傳奇作品。',
        specifications: {
          format: 'LP',
          speed: '33 RPM',
          size: '12 inch',
          weight: '180g'
        },
        weight: 0.3,
        dimensions: {
          length: 31.5,
          width: 31.5,
          height: 0.5
        },
        status: 'active',
        lastSyncAt: new Date()
      },
      {
        productId: '081227971816',
        productName: 'Led Zeppelin - IV (Vinyl)',
        sku: 'VINYL-LZ-IV-001',
        upc: '081227971816',
        artist: 'Led Zeppelin',
        brand: 'World K-POP Center',
        supplier: 'MONO',
        category: 'Vinyl Records',
        price: 750.00,
        costPrice: 500.00,
        stockQuantity: 8,
        reservedQuantity: 2,
        availableQuantity: 6,
        isPublished: true,
        publishedAt: new Date('2024-02-01'),
        productImages: [
          'https://via.placeholder.com/400x400/8B4513/FFFFFF?text=Led+Zeppelin+IV'
        ],
        productVariants: [
          {
            id: 3,
            title: 'Standard Black Vinyl',
            price: 750.00,
            sku: 'VINYL-LZ-IV-BLACK',
            inventory_quantity: 8
          }
        ],
        tags: ['搖滾', '經典', '韓國'],
        description: 'Led Zeppelin 第四張專輯，包含經典歌曲 Stairway to Heaven。',
        specifications: {
          format: 'LP',
          speed: '33 RPM',
          size: '12 inch',
          weight: '180g'
        },
        weight: 0.3,
        status: 'active',
        lastSyncAt: new Date()
      },
      {
        productId: '094638241614',
        productName: 'The Beatles - Abbey Road (50th Anniversary Edition)',
        sku: 'VINYL-BEATLES-AR-50TH',
        upc: '094638241614',
        artist: 'The Beatles',
        brand: 'Avex',
        supplier: 'SK1',
        category: null, // 未分配分類
        price: 1200.00,
        costPrice: 800.00,
        stockQuantity: 0,
        reservedQuantity: 0,
        availableQuantity: 0,
        isPublished: false, // 未上架
        publishedAt: null,
        productImages: [
          'https://via.placeholder.com/400x400/4169E1/FFFFFF?text=Beatles+Abbey+Road'
        ],
        productVariants: [
          {
            id: 4,
            title: '50th Anniversary Edition',
            price: 1200.00,
            sku: 'VINYL-BEATLES-AR-50TH-001',
            inventory_quantity: 0
          }
        ],
        tags: ['粉專排隊', '即將發行', '限量版'],
        description: 'The Beatles Abbey Road 50週年紀念版，即將發行。',
        specifications: {
          format: 'LP',
          speed: '33 RPM',
          size: '12 inch',
          weight: '180g',
          edition: '50th Anniversary'
        },
        weight: 0.35,
        status: 'active',
        lastSyncAt: new Date()
      }
    ];
    
    console.log('\n正在保存模擬商品數據...');
    
    // 批量保存商品
    for (const productData of mockProducts) {
      try {
        const [product, created] = await ProductInfo.findOrCreate({
          where: { productId: productData.productId },
          defaults: productData
        });
        
        if (!created) {
          await product.update(productData);
          console.log(`✅ 商品已更新: ${product.productName}`);
        } else {
          console.log(`✅ 新商品已保存: ${product.productName}`);
        }
      } catch (error) {
        console.error(`❌ 保存商品失敗: ${productData.productName}`, error.message);
      }
    }
    
    // 查詢所有商品
    console.log('\n=== 數據庫中的所有商品 ===');
    const allProducts = await ProductInfo.findAll({
      order: [['createdAt', 'DESC']]
    });
    
    console.log(`找到 ${allProducts.length} 個商品:`);
    allProducts.forEach((product, index) => {
      console.log(`${index + 1}. ${product.productName}`);
      console.log(`   - ID: ${product.productId}`);
      console.log(`   - SKU: ${product.sku}`);
      console.log(`   - UPC: ${product.upc}`);
      console.log(`   - 藝術家: ${product.artist}`);
      console.log(`   - 品牌: ${product.brand}`);
      console.log(`   - 供應商: ${product.supplier}`);
      console.log(`   - 分類: ${product.category || '未分配'}`);
      console.log(`   - 價格: NT$${product.price}`);
      console.log(`   - 庫存: ${product.stockQuantity}`);
      console.log(`   - 上架狀態: ${product.isPublished ? '已上架' : '未上架'}`);
      console.log(`   - 標籤: ${product.tags ? product.tags.join(', ') : '無'}`);
      console.log(`   - 狀態: ${product.status}`);
      console.log('');
    });
    
    // 測試篩選功能
    console.log('=== 測試篩選功能 ===');
    
    // 1. 篩選未上架商品
    const unpublishedProducts = await ProductInfo.findAll({
      where: { isPublished: false }
    });
    console.log(`未上架商品: ${unpublishedProducts.length} 個`);
    
    // 2. 篩選未分配分類的商品
    const uncategorizedProducts = await ProductInfo.findAll({
      where: { category: null }
    });
    console.log(`未分配分類商品: ${uncategorizedProducts.length} 個`);
    
    // 3. 按品牌篩選
    const b2bProducts = await ProductInfo.findAll({
      where: { brand: 'B2B Company Ltd.' }
    });
    console.log(`B2B Company Ltd. 品牌商品: ${b2bProducts.length} 個`);
    
    // 4. 按供應商篩選
    const amiProducts = await ProductInfo.findAll({
      where: { supplier: 'AMI' }
    });
    console.log(`AMI 供應商商品: ${amiProducts.length} 個`);
    
    console.log('\n✅ 商品數據庫測試完成');
    
  } catch (error) {
    console.error('\n❌ 商品數據庫測試失敗:', error.message);
    console.error('詳細錯誤:', error);
  } finally {
    // 關閉數據庫連接
    await sequelize.close();
    console.log('\n數據庫連接已關閉');
  }
}

// 執行測試
testProductDatabase().catch(console.error);
