const axios = require('axios');
const Order = require('../models/Order');
require('dotenv').config();

/**
 * 從 EasyStore API 獲取訂單數據
 */
const fetchOrdersFromEasyStore = async () => {
  try {
    console.log('開始從 EasyStore 同步訂單...');
    
    const response = await axios.get(process.env.EASYSTORE_API_URL, {
      headers: {
        'Authorization': `Bearer ${process.env.EASYSTORE_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.data || !response.data.orders) {
      throw new Error('EasyStore API 返回的數據格式不正確');
    }
    
    // 將 API 數據轉換為我們的數據模型格式
    const orders = response.data.orders.map(order => ({
      orderNumber: order.order_number || order.id,
      customerName: `${order.customer?.first_name || ''} ${order.customer?.last_name || ''}`.trim() || '未知客戶',
      customerEmail: order.customer?.email || null,
      customerPhone: order.customer?.phone || null,
      orderDate: new Date(order.created_at),
      status: mapEasyStoreStatus(order.status),
      totalAmount: parseFloat(order.total_price) || 0,
      items: order.line_items || [],
      shippingAddress: order.shipping_address || {},
      billingAddress: order.billing_address || null,
      paymentMethod: order.payment_method || null,
      paymentStatus: order.payment_status || 'pending',
      notes: order.notes || null,
      easyStoreOrderId: order.id.toString()
    }));
    
    // 批量保存到數據庫
    const savedOrders = [];
    for (const orderData of orders) {
      try {
        const [order, created] = await Order.findOrCreate({
          where: { easyStoreOrderId: orderData.easyStoreOrderId },
          defaults: orderData
        });
        
        if (!created) {
          // 如果訂單已存在，更新相關字段
          await order.update({
            status: orderData.status,
            items: orderData.items,
            paymentStatus: orderData.paymentStatus,
            notes: orderData.notes
          });
        }
        
        savedOrders.push(order);
      } catch (error) {
        console.error(`保存訂單 ${orderData.orderNumber} 時出錯:`, error);
      }
    }
    
    console.log(`成功同步 ${savedOrders.length} 個訂單`);
    return savedOrders;
    
  } catch (error) {
    console.error('從 EasyStore 獲取訂單時出錯:', error);
    throw error;
  }
};

/**
 * 將 EasyStore 的訂單狀態映射到我們的系統狀態
 */
const mapEasyStoreStatus = (easyStoreStatus) => {
  const statusMap = {
    'pending': 'pending',
    'processing': 'processing',
    'shipped': 'shipped',
    'delivered': 'completed',
    'cancelled': 'cancelled',
    'refunded': 'refunded'
  };
  
  return statusMap[easyStoreStatus] || 'pending';
};

/**
 * 獲取單個訂單詳情
 */
const fetchOrderById = async (orderId) => {
  try {
    const response = await axios.get(`${process.env.EASYSTORE_API_URL}/${orderId}`, {
      headers: {
        'Authorization': `Bearer ${process.env.EASYSTORE_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    
    return response.data;
  } catch (error) {
    console.error(`獲取訂單 ${orderId} 詳情時出錯:`, error);
    throw error;
  }
};

module.exports = {
  fetchOrdersFromEasyStore,
  fetchOrderById,
  mapEasyStoreStatus
};
