# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/build

# misc
.DS_Store
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 資料庫相關檔案
*.db
*.sqlite
*.sqlite3

# IDE 檔案
.vscode/
.idea/

# 查詢檔案（可能包含敏感資訊）
query

# 備份檔案
*.bak
*.backup

# 臨時檔案
temp/
tmp/
*.tmp
