const sequelize = require('./src/config/database');
const Order = require('./src/models/Order');
require('dotenv').config();

/**
 * 測試數據庫連接和模擬訂單數據保存
 */

async function testDatabase() {
  console.log('=== 測試數據庫連接和訂單保存 ===');
  
  try {
    // 測試數據庫連接
    console.log('正在測試數據庫連接...');
    await sequelize.authenticate();
    console.log('✅ 數據庫連接成功');
    console.log('數據庫:', process.env.DB_NAME);
    console.log('主機:', process.env.DB_HOST);
    console.log('端口:', process.env.DB_PORT);
    
    // 同步數據庫模型
    console.log('\n正在同步數據庫模型...');
    await sequelize.sync({ force: false }); // force: false 表示不會刪除現有表
    console.log('✅ 數據庫模型同步成功');
    
    // 創建模擬的訂單數據（基於 EasyStore 可能的格式）
    const mockOrderData = {
      orderNumber: 'ES-18506',
      customerName: '測試客戶',
      customerEmail: '<EMAIL>',
      customerPhone: '+886-912-345-678',
      orderDate: new Date(),
      status: 'pending',
      totalAmount: 1250.00,
      items: [
        {
          id: 1,
          name: 'Pink Floyd - The Dark Side of the Moon (Vinyl)',
          sku: 'VINYL-PF-DSOTM',
          quantity: 1,
          price: 850.00,
          variant: 'Black Vinyl'
        },
        {
          id: 2,
          name: 'Led Zeppelin - IV (Vinyl)',
          sku: 'VINYL-LZ-IV',
          quantity: 1,
          price: 400.00,
          variant: 'Remastered'
        }
      ],
      shippingAddress: {
        name: '測試客戶',
        address1: '台北市信義區信義路五段7號',
        address2: '101大樓 50樓',
        city: '台北市',
        province: '台北市',
        country: 'Taiwan',
        zip: '110'
      },
      billingAddress: {
        name: '測試客戶',
        address1: '台北市信義區信義路五段7號',
        city: '台北市',
        province: '台北市',
        country: 'Taiwan',
        zip: '110'
      },
      paymentMethod: 'credit_card',
      paymentStatus: 'paid',
      notes: '請小心包裝黑膠唱片',
      easyStoreOrderId: '18506'
    };
    
    console.log('\n=== 模擬訂單數據 ===');
    console.log(JSON.stringify(mockOrderData, null, 2));
    
    // 保存到數據庫
    console.log('\n正在保存訂單到數據庫...');
    const [savedOrder, created] = await Order.findOrCreate({
      where: { easyStoreOrderId: mockOrderData.easyStoreOrderId },
      defaults: mockOrderData
    });
    
    if (created) {
      console.log('✅ 訂單成功保存到數據庫');
    } else {
      console.log('⚠️ 訂單已存在，正在更新...');
      await savedOrder.update(mockOrderData);
      console.log('✅ 訂單更新成功');
    }
    
    console.log('數據庫訂單ID:', savedOrder.id);
    console.log('EasyStore 訂單ID:', savedOrder.easyStoreOrderId);
    console.log('訂單編號:', savedOrder.orderNumber);
    console.log('客戶名稱:', savedOrder.customerName);
    console.log('總金額:', savedOrder.totalAmount);
    
    // 查詢所有訂單
    console.log('\n=== 查詢所有訂單 ===');
    const allOrders = await Order.findAll({
      order: [['createdAt', 'DESC']]
    });
    
    console.log(`找到 ${allOrders.length} 筆訂單:`);
    allOrders.forEach((order, index) => {
      console.log(`${index + 1}. ${order.orderNumber} - ${order.customerName} - NT$${order.totalAmount}`);
    });
    
    // 測試訂單詳情查詢
    console.log('\n=== 測試訂單詳情查詢 ===');
    const orderDetail = await Order.findOne({
      where: { easyStoreOrderId: '18506' }
    });
    
    if (orderDetail) {
      console.log('訂單詳情:');
      console.log('- 訂單編號:', orderDetail.orderNumber);
      console.log('- 客戶:', orderDetail.customerName);
      console.log('- 電子郵件:', orderDetail.customerEmail);
      console.log('- 電話:', orderDetail.customerPhone);
      console.log('- 狀態:', orderDetail.status);
      console.log('- 總金額:', orderDetail.totalAmount);
      console.log('- 商品數量:', orderDetail.items.length);
      console.log('- 配送地址:', orderDetail.shippingAddress.address1);
      console.log('- 付款方式:', orderDetail.paymentMethod);
      console.log('- 付款狀態:', orderDetail.paymentStatus);
      console.log('- 備註:', orderDetail.notes);
    }
    
    console.log('\n✅ 數據庫測試完成');
    console.log('數據庫功能正常，可以保存和查詢訂單數據');
    
  } catch (error) {
    console.error('\n❌ 數據庫測試失敗:', error.message);
    console.error('詳細錯誤:', error);
    
    // 提供具體的錯誤解決建議
    if (error.name === 'SequelizeConnectionError') {
      console.error('\n連接錯誤解決建議:');
      console.error('1. 檢查 PostgreSQL 是否正在運行');
      console.error('2. 檢查 .env 文件中的數據庫配置');
      console.error('3. 確認數據庫 "UniversalVinyl_MIS" 是否存在');
      console.error('4. 檢查數據庫用戶權限');
    } else if (error.name === 'SequelizeAccessDeniedError') {
      console.error('\n權限錯誤解決建議:');
      console.error('1. 檢查數據庫用戶名和密碼');
      console.error('2. 確認用戶有訪問數據庫的權限');
    }
  } finally {
    // 關閉數據庫連接
    await sequelize.close();
    console.log('\n數據庫連接已關閉');
  }
}

// 執行測試
testDatabase().catch(console.error);
