const sequelize = require('./src/config/database');
const ProductInfo = require('./src/models/ProductInfo');
require('dotenv').config();

async function checkProductsDatabase() {
  console.log('=== 檢查商品數據庫狀態 ===');
  
  try {
    await sequelize.authenticate();
    console.log('✅ 數據庫連接成功');
    
    await sequelize.sync();
    console.log('✅ 數據庫模型同步成功');
    
    const products = await ProductInfo.findAll({
      order: [['createdAt', 'DESC']]
    });
    
    console.log(`\nPostgreSQL 中的商品數量: ${products.length}`);
    
    if (products.length > 0) {
      console.log('\n=== PostgreSQL 中的商品列表 ===');
      products.forEach((product, index) => {
        console.log(`${index + 1}. ${product.productName}`);
        console.log(`   - UPC: ${product.upc}`);
        console.log(`   - SKU: ${product.sku}`);
        console.log(`   - 庫存: ${product.stockQuantity}`);
        console.log(`   - 上架狀態: ${product.isPublished ? '已上架' : '未上架'}`);
        console.log(`   - 創建時間: ${product.createdAt}`);
        console.log('');
      });
    } else {
      console.log('❌ PostgreSQL 中沒有商品數據');
    }
    
    // 檢查是否有重複的 UPC
    const upcCounts = {};
    products.forEach(product => {
      if (product.upc) {
        upcCounts[product.upc] = (upcCounts[product.upc] || 0) + 1;
      }
    });
    
    const duplicateUPCs = Object.entries(upcCounts).filter(([upc, count]) => count > 1);
    if (duplicateUPCs.length > 0) {
      console.log('⚠️ 發現重複的 UPC:');
      duplicateUPCs.forEach(([upc, count]) => {
        console.log(`   - UPC ${upc}: ${count} 個商品`);
        const duplicateProducts = products.filter(p => p.upc === upc);
        duplicateProducts.forEach(p => {
          console.log(`     * ${p.productName} (ID: ${p.id})`);
        });
      });
    } else {
      console.log('✅ 沒有發現重複的 UPC');
    }
    
  } catch (error) {
    console.error('❌ 檢查失敗:', error);
  } finally {
    await sequelize.close();
    console.log('\n數據庫連接已關閉');
  }
}

checkProductsDatabase();
